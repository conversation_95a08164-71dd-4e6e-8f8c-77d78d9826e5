#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置合并功能
"""

# 模拟导入
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_inheritance():
    """测试配置继承和合并"""
    
    # 模拟基类
    class TaskBase:
        @classmethod
        def get_default_biz_config(cls):
            return {}
    
    # 模拟父类
    class TaskBangHuiShengChan(TaskBase):
        @classmethod
        def get_default_biz_config(cls):
            # 获取父类的默认配置
            config = super().get_default_biz_config()
            # 添加当前类特有的配置
            config.update({
                "材料号名字": "",
            })
            return config
    
    # 模拟子类1 - 有自己的配置
    class TaskCaiLiao(TaskBangHuiShengChan):
        pass  # 没有自己的 get_default_biz_config，会继承父类的
    
    # 模拟子类2 - 有自己的配置
    class TaskWaKuang(TaskBangHuiShengChan):
        @classmethod
        def get_default_biz_config(cls):
            # 获取父类的默认配置
            config = super().get_default_biz_config()
            # 添加当前类特有的配置
            config.update({
                "锄头数": 3,
                "物材挖完停止": False,
            })
            return config
    
    # 测试配置
    print("=== 配置继承测试 ===")
    
    print("\n1. TaskBangHuiShengChan 配置:")
    base_config = TaskBangHuiShengChan.get_default_biz_config()
    print(f"   {base_config}")
    
    print("\n2. TaskCaiLiao 配置 (继承父类):")
    cailiao_config = TaskCaiLiao.get_default_biz_config()
    print(f"   {cailiao_config}")
    
    print("\n3. TaskWaKuang 配置 (合并父类+自己):")
    wakuang_config = TaskWaKuang.get_default_biz_config()
    print(f"   {wakuang_config}")
    
    # 验证
    assert "材料号名字" in cailiao_config, "TaskCaiLiao 应该包含父类的配置"
    assert "材料号名字" in wakuang_config, "TaskWaKuang 应该包含父类的配置"
    assert "锄头数" in wakuang_config, "TaskWaKuang 应该包含自己的配置"
    assert "物材挖完停止" in wakuang_config, "TaskWaKuang 应该包含自己的配置"
    
    print("\n✅ 所有测试通过！配置继承和合并工作正常。")

if __name__ == "__main__":
    test_config_inheritance()
