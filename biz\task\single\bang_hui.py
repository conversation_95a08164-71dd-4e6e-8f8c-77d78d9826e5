from biz.exception import TaskFinalStatusException
from biz.task.__base import TaskBase
from biz.constants.constants import *
from biz.task.team.fu_ben import TaskFuBen
from utils import *
from biz.obj.worker import Worker


class TaskBangHuiShengChan(TaskBase):
    TASK_NAME = "帮会生产"
    NEED_AVOID_FIGHT = True
    IS_TASK_FIX_EQUIP_BB_ENABLE = True

    @classmethod
    def run(cls, wk: Worker):
        cls.leave_team(wk)
        while wk.done_count < cls.get_task_setting_count(wk):
            try:
                cls.recv_task(wk)
            except:
                wk.record("任务完成达到上限")
                break
            try:
                cls.do_task(wk)
            except TaskFinalStatusException as e:
                cls.refresh_task_list(wk)
            except Exception as e:
                wk.record(f"做任务时发生异常:{e}")
                break
            msleep(400)

    @classmethod
    def after_run(cls, wk: Worker):
        wk.record("任务完成, 返回开封...")
        cls.close_pages(wk)
        cls.from_bang_hui_to_kai_feng(wk)
        super().after_run(wk)

    @classmethod
    def get_task_setting_count(cls, wk: Worker):
        return 200

    @classmethod
    def recv_task(cls, wk: Worker):
        wk.record("正在找林冲接任务...")
        if not cls.is_in_bang_hui(wk):
            cls.go_to_bang_hui(wk)
        # 这里不关对话主要是为了能看到任务完成信息
        cls.find_way_npc(wk, "林冲", ["有事就说吧"], reply_first=True,
                         input=True, until_func=cls.end_condition, close_talk=False)
        cls.close_other_talk(wk)

    @classmethod
    def end_condition(cls, wk: Worker) -> bool:
        if cls.is_talk_show_info(wk, "我可不好意思再劳烦"):
            wk.done_count = 20
            raise Exception("任务完成达到上限")
        return False

    @classmethod
    def do_task(cls, wk: Worker):
        cls.open_task_page(wk)
        cls.avoid_other_task_disturb(wk)
        if cls.region_task_status_get_status(wk) != "进行中":
            cls.close_pages(wk)
            return
        # 根据不同的任务来做操作
        if cls.region_task_desc_find_str(wk, "拜访"):
            return cls.do_task_visit(wk)
        if cls.region_task_desc_find_str(wk, "寻找"):  # 收集物品
            return cls.do_task_trade_thing(wk)
        if cls.region_task_desc_find_str(wk, "把送到的"):
            return cls.do_task_send_mail(wk)
        if cls.region_task_status_get_npc_name(wk) == "江湖万事通":
            cls.do_task_query_jhwst(wk)  # 单挑 和 找人
        if cls.region_task_desc_find_str(wk, "当前应该在"):
            return cls.do_task_find_people(wk)
        if cls.region_task_desc_find_str(wk, "要多加小心"):
            return cls.do_task_fight_people(wk)
        wk.record("识别子任务失败...")
        msleep(1000)

    @classmethod
    def do_task_visit(cls, wk: Worker):
        npc_name = cls.region_task_status_get_npc_name(wk)
        wk.record(f"拜访 {npc_name}")
        cls.close_pages(wk)
        cls.from_bang_hui_to_kai_feng(wk)
        cls.task_npc_find_way_run_away(
            wk, "拜访", npc_name=npc_name, color=COLOR_TALK_ITEM_TASK)

    @classmethod
    def do_task_send_mail(cls, wk: Worker):
        npc_name = cls.region_task_status_get_npc_name(wk)
        wk.record(f"送信函给 {npc_name}")
        cls.close_pages(wk)
        cls.from_bang_hui_to_kai_feng(wk)
        cls.task_npc_find_way_run_away(
            wk, "送信函", npc_name=npc_name, color=COLOR_TALK_ITEM_TASK)

    @classmethod
    def from_cur_map_to_kai_feng(cls, wk: Worker):
        cls.from_bang_hui_to_kai_feng(wk)

    @classmethod
    def do_task_find_people(cls, wk: Worker):
        wk.record("找人...")
        cls.close_pages(wk)
        if cls.is_in_bang_hui(wk):
            cls.from_bang_hui_to_kai_feng(wk)
            if cls.cur_map(wk) != "开封":
                return
        cls.task_npc_find_way_run_away(wk, "找寻", color=COLOR_TALK_ITEM_TASK)
        if wk.is_fight:
            cls.fight_operation(wk)
            cls.do_fix_bb(wk)

    @classmethod
    def do_task_fight_people(cls, wk: Worker):
        wk.record("单挑...")
        cls.close_pages(wk)
        if cls.is_in_bang_hui(wk):
            cls.from_bang_hui_to_kai_feng(wk)
            if cls.cur_map(wk) != "开封":
                return
        cls.task_npc_find_way_run_away(wk, "看招", color=COLOR_TALK_ITEM)
        msleep(800)
        if wk.is_fight:
            cls.fight_operation(wk)
            cls.do_fix_bb(wk)

    @classmethod
    def do_task_trade_thing(cls, wk: Worker):
        wk.record("寻找物品...")
        thing_name = ""
        for i in range(100):
            thing_name = cls.region_task_desc_ocr(wk, COLOR_GREEN)
            if thing_name == "" or thing_name not in cls.THING_NO.keys():
                wk.record(f"识别的物品名:{thing_name} 不存在")
                msleep(1000)
                continue
            break
        if not thing_name:
            wk.record("识别物品名失败")
            return
        done_count, need_count = cls.region_task_desc_thing_num(wk)
        wk.record(f"物品名:{thing_name} 数量:{done_count}/{need_count}")
        if done_count >= need_count:
            wk.record("寻找物品完成")
            return
        cls.close_pages(wk)
        # 识别材料号名称
        cai_liao_hao_name = wk.cfg_plan_task["材料号名字"] or wk.ocr(
            *RECT_CAI_LIAO_HAO, COLOR_WHITE+"|"+COLOR_NAME_VIP, zk=ZK_NAME_11)
        if not cai_liao_hao_name:
            wk.record("没找到材料号...")
            wk.key_press(VK_F12)
            msleep(5000, 10000)
            return
        # 构造交易金额字符串
        thing_no = cls.THING_NO[thing_name]
        thing_count = need_count - done_count
        amount_str = "%d%02d" % (thing_no, thing_count)
        res = cls.trade_with_player(
            wk, cai_liao_hao_name, amount_str, RECT=RECT_CAI_LIAO_HAO)
        if res == -1:
            wk.record("没找到材料号, 等待中...")
            msleep(5000, 10000)

    @classmethod
    def is_cur_task_desc(cls, wk: Worker):
        if cls.region_task_desc_find_str(wk, "物资箱", COLOR_GREEN):
            return False
        if cls.region_task_desc_find_str(wk, "帮会领地", COLOR_RED):
            return True
        return False

    @classmethod
    def shout_bang_hui(cls, wk: Worker, info: str):
        wk.record(f"帮会喊话...")
        if not wk.find_str(*RECT_SHOUT, "帮会", COLOR_TASK_NAME, zk=ZK_ALL_9):
            wk.move_click(*POS_SWITCH_CHANNEL)
            wk.move_click(*POS_CHANNEL_BANG_HUI)
        wk.move_click(*POS_CHAT)
        wk.send_string(info, sync=True, lock=True)
        msleep(500)
        wk.move_click(*POS_CHAT)
        wk.key_press(VK_ENTER)

    @classmethod
    def change_to_origin_task(cls, wk: Worker):
        if cls.region_task_list_find_pic_offset_click(wk, "帮会生产任务.bmp", timeout=200):
            wk.record("已切回 原任务")
            msleep(400)
            return True
        return False

    @classmethod
    def ready_overlap_auth(cls, wk: Worker):
        if cls.cur_map(wk) in OUTSIDE_MAP_LIST:
            return super().ready_overlap_auth(wk)
        return TaskFuBen.ready_overlap_auth(wk)

    @classmethod
    def adjust_color(cls, wk: Worker, pic_list_length: int, delta_color: str):
        return TaskFuBen.adjust_color(wk, pic_list_length, delta_color)
    
    @classmethod
    def get_default_biz_config(cls):
        # 获取父类的默认配置
        config = super().get_default_biz_config()
        # 添加当前类特有的配置
        config.update({
            "材料号名字": "",
        })
        return config

    @classmethod
    def cfg_read(cls, plan_name: str):
        super().cfg_read(plan_name)
        settings.wnd_main.edt_cai_liao_name.setText(cls.CONFIG["材料号名字"])
       
    @classmethod
    def cfg_save(cls, plan_name: str):
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["材料号名字"] = settings.wnd_main.edt_cai_liao_name.text()
        super().cfg_save(plan_name)

    THING_NO = {
        "兰香草": 10,
        "玉石骰": 11,
        "神龙丹": 12,
        "半春莲": 13,
        "某某剑法残谱": 14,
        "大血藤": 15,
        "青黛": 16,
        "小龙女的画像": 17,
        "熊胆": 18,
        "白驼山壮骨粉": 19,
        "蛇胆": 20,
        "曼陀罗": 21,
        "破碎的兽皮": 22,
        "络石纹": 23,
        "犬牙": 24,
        "铁兰花": 25,
        "红花": 26,
        "猴儿酒": 27,
        "孔雀胆": 28,
        "马奶酒": 29,
        "用旧的大马士革刀": 30,
        "紫珠": 31,
        "眼罩": 32,
        "相思玉": 33,
        "香附子": 34,
        "燕窝": 35,
        "古墓散卷": 36,
        "破烂的头盔": 37,
        "破包裹": 38,
        "烂背篓": 39,
        "花岗岩": 40,
        "狗骨头": 41,
        "赭石": 42,
        "没把的锄头": 43,
    }
    THING_NO_REVERSE = {
        10: "兰香草",
        11: "玉石骰",
        12: "神龙丹",
        13: "半春莲",
        14: "某某剑法残谱",
        15: "大血藤",
        16: "青黛",
        17: "小龙女的画像",
        18: "熊胆",
        19: "白驼山壮骨粉",
        20: "蛇胆",
        21: "曼陀罗",
        22: "破碎的兽皮",
        23: "络石纹",
        24: "犬牙",
        25: "铁兰花",
        26: "红花",
        27: "猴儿酒",
        28: "孔雀胆",
        29: "马奶酒",
        30: "用旧的大马士革刀",
        31: "紫珠",
        32: "眼罩",
        33: "相思玉",
        34: "香附子",
        35: "燕窝",
        36: "古墓散卷",
        37: "破烂的头盔",
        38: "破包裹",
        39: "烂背篓",
        40: "花岗岩",
        41: "狗骨头",
        42: "赭石",
        43: "没把的锄头"
    }


class TaskBangHuiSheShi(TaskBangHuiShengChan):
    TASK_NAME = "帮会设施"
    NEED_AVOID_FIGHT = False
    IS_TASK_FIX_EQUIP_BB_ENABLE = False
    TASK_THINGS = ["金创药", "桃子", "罗汉明子膏", "飘火逸灵丸", "紫阳融雪丸", "还魂续命丸",
                   "抢来的衣服", "旧马鞍", "狗皮", "十字肉包子", "绝情花粉", "开山锤",
                   "某某剑法残谱", "小龙女的画像", "用旧的大马士革刀", "白驼山壮骨粉", "相思玉",
                   "破碎的兽皮", "古墓散卷", "装饰用手套", "破烂的头盔",
                   "玉石骰", "刺帽针", "神龙丹", "眼罩"]
    IDX_NEED_BUY = 5

    @classmethod
    def run(cls, wk: Worker):
        wk.lack_thing = set()
        cls.leave_team(wk)
        while wk.done_count < cls.get_task_setting_count(wk):
            try:
                cls.recv_task(wk)
            except:
                wk.record("任务完成达到上限")
                break

    @classmethod
    def recv_task(cls, wk: Worker):
        wk.record("正在找凌振接任务...")
        if not cls.is_in_bang_hui(wk):
            cls.go_to_bang_hui(wk)
        # 这里不关对话主要是为了能看到任务完成信息
        cls.find_way_npc(wk, "凌振", ["领取设施"], reply_first=True, input=True,
                         until_func=cls.end_condition, close_talk=False, click_doing=True)
        if cls.is_talk_show_info(wk, "现在没有正在修建的设施"):
            raise Exception("没有正在修建的设施")
        cls.close_other_talk(wk)

    @classmethod
    def do_task_trade_thing(cls, wk: Worker):
        wk.record("寻找物品...")
        thing_name = ""
        for i in range(100):
            thing_name = cls.region_task_desc_ocr(wk, COLOR_GREEN)
            if thing_name == "" or thing_name not in cls.TASK_THINGS:
                wk.record(f"识别的物品名:{thing_name} 不存在")
                msleep(1000)
                continue
            break
        if not thing_name:
            wk.record("识别物品名失败")
            return
        done_count, need_count = cls.region_task_desc_thing_num(wk)
        wk.record(f"物品名:{thing_name} 数量:{done_count}/{need_count}")

        if done_count >= need_count:
            wk.record("寻找物品完成")
            return
        wk.lack_thing.add(thing_name)
        lack_thing_str = f"{wk.lack_thing}".strip("{}").replace("'", "")
        cls.shout_bang_hui(wk, f"#143 缺少物品: {lack_thing_str}")


class TaskCaiLiao(TaskBangHuiShengChan):
    TASK_NAME = "帮会材料"
    NEED_AVOID_FIGHT = False
    RECT_REFER = (806, 62, 829, 83)
    MAP_POS_CAI_LIAO = (662, 127)

    @classmethod
    def run(cls, wk: Worker):
        wk.lack_thing = set()
        if not cls.is_in_bang_hui(wk):
            cls.go_to_bang_hui(wk)
        cls.close_pages(wk)

        for i in range(100000000):
            msleep(800)
            if wk.is_stuck and not wk.find_pic(*cls.RECT_REFER, "天机营.bmp"):
                wk.record("正在跑回固定点...")
                cls.big_map_click(wk, *cls.MAP_POS_CAI_LIAO)
            if wk.cfg_plan_task["材料号喊话"] and i % 50 == 0:
                info = wk.cfg_plan_task["材料号喊话内容"]
                cls.shout_bang_hui(wk, info)
            if cls.click_confirm(wk, timeout=0, RECT=RECT_POPUP):
                msleep(500)
                cls.trade_cai_liao(wk)
            cls.close_system_page(wk)
            cls.close_other_talk(wk)

    @classmethod
    def wait_against_lock(cls, wk: Worker):
        # 重写一下, 这里单纯只用来等待对方锁定
        wk.record("开始交易, 等待对方锁定...")
        for _ in range(20):
            if wk.find_pic(*RECT_TRADE_LOCK_AGAINST, "交易锁定.bmp"):
                return True
            if not cls.is_trade_page_open(wk):  # 说明对方取消或者确认
                return False
            msleep(500)
        return False

    @classmethod
    def wait_against_confirm(cls, wk: Worker):
        wk.record("正在等待对方确认...")
        for _ in range(20):
            if not cls.is_trade_page_open(wk):  # 说明对方取消或者确认
                return True
            cls.click_confirm(wk)  # 确保自己这边能点的确定都点了
            msleep(600)
        return False

    @classmethod
    def ocr_trade_amount(cls, wk: Worker):
        return wk.ocr(*RECT_TRADE_AMOUNT_AGAINST, COLOR_WHITE, zk=ZK_DIGIT_11)

    @classmethod
    def trade_cai_liao(cls, wk: Worker):
        player_name = ""
        for _ in range(3):
            msleep(600)
            player_name = cls.ocr_trade_player_name(wk)
            if player_name:
                break
            cls.click_cancel(wk)  # 如果这里进了别人的邀请
            cls.close_system_page(wk)  # 关闭系统页面
        if not cls.wait_against_lock(wk):
            wk.record(f"等待对方锁定超时, 对方名称: {player_name}")
            cls.close_pages(wk)
            return
        # 识别对方的金额
        amount = cls.ocr_trade_amount(wk)
        if len(amount) != 4:
            wk.record(f"对方输入金额无效, 取消交易")
            cls.close_pages(wk)
            return
        wk.record(f"对方名称: {player_name}, 交易金额: {amount}")
        # 解析出 物品名称 和 物品数量
        thing_name, thing_amount = cls.THING_NO_REVERSE[int(
            amount[:2])], int(amount[2:])
        wk.record(f"物品名:{thing_name} 数量:{thing_amount}")
        if thing_amount >= 11:
            wk.record(f"物品数量过多, 取消交易, 对方名称: {player_name}, 交易金额: {amount}")
            cls.close_pages(wk)
            return
        # 搜索背包, 找到物品
        pic_name = wk.match_pic(f"*_{thing_name}.bmp")
        if not cls.trade_thing_specify_amount(wk, pic_name, thing_amount):
            wk.record(f"缺少物品 {thing_name}, 取消交易")
            wk.lack_thing.add(thing_name)
            lack_thing_str = f"{wk.lack_thing}".strip("{}").replace("'", "")
            wk.record(f"当前缺少物品列表: {lack_thing_str}")
            cls.click_cancel(wk, timeout=0, RECT=RECT_FULL)
            cls.close_pages(wk)
            cls.shout_bang_hui(wk, f"#143 缺少物品: {lack_thing_str}")
            return
        # 点击锁定和确认
        cls.trade_with_amount(wk, amount)
        # 等待对方点确认
        if not cls.wait_against_confirm(wk):
            wk.record("等待对方确认超时, 主动取消交易")
            cls.click_cancel(wk, RECT=RECT_FULL)
            return
        wk.record(
            f"发材料成功, 物品名:{thing_name} 数量:{thing_amount} 对方名称: {player_name}")

    @classmethod
    def get_default_biz_config(cls):
        # 获取父类的默认配置
        config = super().get_default_biz_config()
        # 添加当前类特有的配置
        config.update({
            "材料号喊话": True,
            "材料号喊话内容": "#143 帮会材料号在一线帮会已就位  有空的兄弟姐妹可以过来跑生产了 需要的任务品 点我交易#127",
        })
        return config

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件到控件
        super().cfg_read(plan_name)
        settings.wnd_main.groupBox_shout_bang_hui.setChecked(
            cls.CONFIG["材料号喊话"])
        settings.wnd_main.edt_shout_bang_hui.setPlainText(
            cls.CONFIG["材料号喊话内容"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件到文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["材料号喊话"] = settings.wnd_main.groupBox_shout_bang_hui.isChecked()
        cls.CONFIG["材料号喊话内容"] = settings.wnd_main.edt_shout_bang_hui.toPlainText()
        super().cfg_save(plan_name)


class TaskWaKuang(TaskBangHuiShengChan):
    TASK_NAME = "帮会挖矿"
    NEED_AVOID_FIGHT = False

    @classmethod
    def run(cls, wk: Worker):
        # 先去到矿洞
        if cls.cur_map(wk) != "矿洞":
            if not cls.is_in_bang_hui(wk):
                cls.go_to_bang_hui(wk)
            cls.cross_map_by_talk(wk, "矿洞", npc_name="陶宗旺")
        # 挖矿
        cls.mine(wk)

    @classmethod
    def mine(cls, wk: Worker):
        flag = True
        while True:
            if flag:
                wk.record("正在跑向采矿点...")
                cls.big_map_click(wk, 560, 176)
                flag = False
            if wk.cfg_plan_task["物材挖完停止"]:
                if cls.is_system_broad_show(wk, "物材被挖完了", COLOR_GOLD):
                    wk.record("物材被挖完了, 退出挖矿...")
                    break
            if wk.is_stuck:
                if not cls.bag_use_item(wk, "矿镐.bmp", check_twice=True, is_confirm=False, is_close=False, bag_open_fail_true=True):
                    cls.buy_kuang_gao(wk)
                    flag = True
                    continue
                msleep(400)
                cls.click_close_pic(wk)  # 关闭背包
                msleep(800)
                if cls.is_system_broad_show(wk, "这里没有矿点"):
                    flag = True
                    continue
                if cls.is_popup_show_info(wk, "包裹已满") or cls.is_bag_full(wk):
                    wk.record("背包满了, 退出挖矿...")
                    cls.click_confirm(wk)
                    break
                wk.record("使用矿镐成功, 正在挖矿...")
                msleep(2000)
                for i in range(30):
                    if not wk.find_pic(*RECT_FULL, "取消.bmp"):
                        break
                    msleep(1000)
            msleep(600)
        cls.back_to_kai_feng(wk)

    @classmethod
    def buy_kuang_gao(cls, wk: Worker):
        kuang_gao_num = wk.cfg_plan_task["锄头数"]
        wk.record(f"矿镐不足，购买{kuang_gao_num}个矿镐...")
        cls.buy_thing(wk, "矿镐", kuang_gao_num)

    @classmethod
    def split_num_to_arr(cls, num: int):
        # 把num分为数组，因为最大一次只能买1个
        return [1] * num

    @classmethod
    def input_number(cls, wk: Worker, num: int):
        # 因为不用输入数量, 但要覆盖父类方法
        pass

    @classmethod
    def get_default_biz_config(cls):
        # 获取父类的默认配置
        config = super().get_default_biz_config()
        # 添加当前类特有的配置
        config.update({
            "锄头数": 3,
            "物材挖完停止": False,
        })
        return config

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件 -> 控件
        super().cfg_read(plan_name)
        settings.wnd_main.spin_count_wa_kuang_chu_tou.setValue(
            cls.CONFIG["锄头数"])
        settings.wnd_main.chk_wa_kuang_stop.setChecked(cls.CONFIG["物材挖完停止"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件 -> 文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["锄头数"] = settings.wnd_main.spin_count_wa_kuang_chu_tou.value()
        cls.CONFIG["物材挖完停止"] = settings.wnd_main.chk_wa_kuang_stop.isChecked()
        super().cfg_save(plan_name)


class TaskJiShi(TaskBangHuiShengChan):
    TASK_NAME = "上缴基石"
    IS_FREE = True
    NEED_AVOID_FIGHT = False
    RECT_GIVE_JISHI = (400, 213, 807, 538)

    @classmethod
    def run(cls, wk: Worker):
        if not cls.is_in_bang_hui(wk):
            cls.go_to_bang_hui(wk)
        cls.find_way_npc(wk, "陶宗旺", ["上缴基石"], until_func=cls.end_condition)
        for _ in range(12):
            if not wk.find_pic_r_click(*cls.RECT_GIVE_JISHI, "基石.bmp", timeout=300):
                break
            if cls.click_confirm(wk, RECT=cls.RECT_GIVE_JISHI):
                msleep(300)
        cls.click_confirm(wk)
        cls.close_pages(wk)


class TaskBangHuiYangMa(TaskBangHuiShengChan):
    TASK_NAME = "帮会养马"
    NEED_AVOID_FIGHT = False

    @classmethod
    def run(cls, wk: Worker):
        if wk.cfg_plan_task["养马持续监测"]:
            wk.record("持续监测养马...")
            while True:
                cls.do_yang_ma(wk)
                wk.record("操作完成, 等待30分钟后做下一次检查...")
                msleep(30*60*1000)  # 每30分钟操作一次
        else:
            wk.record("养马一次性操作, 可配置持续监测")
            cls.do_yang_ma(wk)

    @classmethod
    def do_yang_ma(cls, wk: Worker):
        if not cls.is_in_bang_hui(wk):
            cls.go_to_bang_hui(wk)
        cls.find_way_npc(wk, "牧场管理员", ["管理牧场"])
        if not cls.is_muchang_page_open(wk, timeout=800):
            wk.record("牧场页面打开失败, 离开帮会重进...")
            return
        cls.do_task_jiacaoliao(wk)
        cls.click_confirm(wk)

        cls.do_task_shiyao(wk)
        cls.click_confirm(wk)

        cls.do_task_kaishui(wk)
        cls.click_confirm(wk)

        cls.close_pages(wk)
        cls.from_bang_hui_to_kai_feng(wk)
        
    @classmethod
    def is_muchang_page_open(cls, wk: Worker, timeout=0):
        return wk.find_multi_color(*RECT_MUCHANG_PAGE, MCOLOR_MUCHANG_PAGE, timeout=timeout)

    @classmethod
    def do_task_jiacaoliao(cls, wk: Worker):
        wk.record("加草料...")
        wk.move_click(*POS_MC_JIACAOLIAO)
        msleep(600)
        wk.move_click(*POS_MC_JIACAOLIAO_TF)
        msleep(600)
        wk.send_string("999999")
        msleep(600)
        wk.move_click(*POS_MC_JIACAOLIAO_CONFIRM)
        msleep(600)
        if cls.click_confirm(wk):
            wk.record("加草料成功")
        else:
            wk.record("加草料失败")

    @classmethod
    def do_task_shiyao(cls, wk: Worker):
        wk.record("施药除虫...")
        wk.move_click(*POS_MC_SHIYAO)
        msleep(600)
        if cls.click_confirm(wk):
            wk.record("施药除虫成功")
        else:
            wk.record("施药除虫失败")

    @classmethod
    def do_task_kaishui(cls, wk: Worker):
        wk.record("开掘水源...")
        wk.move_click(*POS_MC_KAISHUI)
        msleep(600)
        if cls.click_confirm(wk):
            wk.record("开掘水源成功")
        else:
            wk.record("开掘水源失败")

    @classmethod
    def get_default_biz_config(cls):
        # 获取父类的默认配置
        config = super().get_default_biz_config()
        # 添加当前类特有的配置
        config.update({
            "养马持续监测": False,
        })
        return config

    @classmethod
    def cfg_read(cls, plan_name: str):  # 文件 -> 控件
        super().cfg_read(plan_name)
        settings.wnd_main.chk_yangma_keep.setChecked(cls.CONFIG["养马持续监测"])

    @classmethod
    def cfg_save(cls, plan_name: str):  # 控件 -> 文件
        cls.CONFIG = cls.get_default_biz_config()
        cls.CONFIG["养马持续监测"] = settings.wnd_main.chk_yangma_keep.isChecked()
        super().cfg_save(plan_name)
