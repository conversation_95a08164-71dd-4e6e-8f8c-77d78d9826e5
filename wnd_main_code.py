# 标准库
import copy
import pickle
from threading import Thread
import requests

# 第三方库
from PySide2.QtWidgets import (
    QMainWindow,
    QLabel,
    QComboBox,
    QMenu,
    QTableWidgetItem,
    QDialog,
    QTextBrowser,
    QHBoxLayout,
    QMessageBox,
    QSystemTrayIcon,
    QAction,
    QPushButton,
    QSlider,
    QFileDialog,
    QSpinBox,
    QTreeWidgetItem,
    QTreeWidgetItemIterator,
    QCheckBox,
    QHeaderView,
    QStyledItemDelegate,
)
from PySide2.QtGui import QCursor, QIcon, QTextCursor, QCloseEvent, QColor, QPaintEvent, QPixmap, QPainter, QRegExpValidator
from PySide2.QtCore import Signal, QTimer, Qt, QEvent, QSize, QRegExp
from memwin.xprocess import XProcess
import webbrowser

# 本地库
from pyqt_update_wnd.wnd_update_code import WndUpdateSoftware
from wnd_plan_confirm_code import WndPlanConfirm
from ui.wnd_main import Ui_WndMain
from biz.obj.worker import Worker
from biz.constants.constants import *
from biz.constants import constants
from threads import *
from utils import *
from const.const import *
import settings
from biz.task import *
from biz.team.team import Team
from memwin.xapi import *

requests.packages.urllib3.disable_warnings()

class WndMain(QMainWindow, Ui_WndMain):
    sig_cell = Signal(int, int, str)
    sig_info = Signal(str)
    sig_close = Signal()
    sig_arrange = Signal()
    sig_rights = Signal(str, bool)
    sig_title = Signal(str)
    sig_remove = Signal(Worker)
    sig_account = Signal(dict)

    def __init__(self):
        self.initialed = False
        self.show_msg = True
        super().__init__()
        # 安装界面
        self.setupUi(self)
        # 初始化自定义信号槽
        self.init_custom_sig_slot()
        # 初始化实例属性
        self.init_instance_field()
        # 初始化界面控件
        self.init_widgets()
        # 初始化菜单
        self.init_menus()
        # 初始化信号槽
        self.init_sig_slot()
        # 设置为首页
        self.stack_widget.setCurrentIndex(0)
        # 显示欢迎信息
        self.show_info("初始化完成, 欢迎使用!")
        # 开启热键线程
        Thread(target=self.thd_hotkey, daemon=True).start()
        # 开启心跳线程
        Thread(target=self.thd_heart_beat, daemon=True).start()
        # 安装事件过滤器
        self.tbe_console.installEventFilter(self)
        for obj in self.findChildren(QSpinBox):
            obj.installEventFilter(self)
        for obj in self.findChildren(QComboBox):
            obj.installEventFilter(self)
        for obj in self.findChildren(QSlider):
            obj.installEventFilter(self)
        for obj in self.findChildren(QComboBox):
            if isinstance(obj, AutoSortComboBox):
                continue
            obj.setView(QListView())
            # obj.setStyleSheet('''
            #     QComboBox QAbstractItemView::item {
            #         height: 18px;
            #         color: rgb(0, 0, 0);
            #     }
            # ''')

    def closeEvent(self, event: QCloseEvent):
        self.timer_cur.stop()
        self.timer_ds.stop()
        self.timer_info.stop()
        self.diag.close()
        def thd_stop(wk: Worker):
            wk.record("被强制终止")
            wk.thread.end()
        thd_list = []
        for wk in settings.worker_list:
            if wk and not wk.is_end:
                t = Thread(target=thd_stop, args=(wk,), daemon=True)
                t.start()
                thd_list.append(t)
        for t in thd_list:
            t.join()
        time.sleep(0.5)
        if len(thd_list):
            self.show_info("等待所有线程结束...")
            time.sleep(1)
            self.show_info("所有线程已结束")
            
    def paintEvent(self, event: QPaintEvent):
        idx = settings.cfg_common["软件背景"]
        if not idx:
            super().paintEvent(event)
            return
        # 值是越低越透明
        opacity = 1 - settings.cfg_common["背景透明度"] / 100
        img_name = self.back_img if self.back_img else f":/back{idx}.jpg"
        pix_map = QPixmap(img_name).scaled(self.size())
        painter = QPainter(self)
        painter.setOpacity(opacity)
        painter.drawPixmap(self.rect(), pix_map)

    def resizeEvent(self, event):
        """当窗口大小发生变化时调用此方法"""
        super().resizeEvent(event)
        if not self.initialed:
            return
        new_size = event.size()
        if new_size.width() == settings.cfg_common["窗口宽"] and new_size.height() == settings.cfg_common["窗口高"]:
            return
        settings.cfg_common["窗口宽"] = new_size.width()
        settings.cfg_common["窗口高"] = new_size.height()
        dict_to_json_file(settings.cfg_common, PATH_SOFTWARE_COMMON)

    def eventFilter(self, obj, event):
        if event.type() == QEvent.Wheel:
            if obj is self.tbe_console:
                delta = 1 if event.angleDelta().y() > 0 else -1
                if event.modifiers() & Qt.ShiftModifier:
                    self.tbe_console.horizontalScrollBar().setValue(self.tbe_console.horizontalScrollBar().value() - delta)
                    return True
                else:
                    self.tbe_console.verticalScrollBar().setValue(self.tbe_console.verticalScrollBar().value() - delta)
                    return True
            if obj.inherits("QSpinBox") or obj.inherits("QComboBox") or obj.inherits("QSlider"):
                return True
        return super().eventFilter(obj, event)

    # 读取配置（文件到控件）
    def common_cfg_read(self):
        dir_create(PATH_SOFTWARE_CONFIG)
        # 先读文件
        read_common_cfg = json_file_to_dict(PATH_SOFTWARE_COMMON)
        settings.cfg_common = copy.deepcopy(settings.default_cfg_common)
        for key, value in read_common_cfg.items():
            if key not in settings.cfg_common:
                continue
            if key == "游戏账号信息":
                settings.cfg_common["游戏账号信息"] = []
                for account_info in read_common_cfg["游戏账号信息"]:
                    settings.cfg_common["游戏账号信息"].append(account_info)
            else:  # 其它的都逐key更新就好
                settings.cfg_common[key] = value
        if not os.path.exists("config/account_infos.xdtop"):
            # 旧方法
            for cfg_account in settings.cfg_common["游戏账号信息"]:
                if not cfg_account:
                    continue
                row = cfg_account["row"]
                if not self.tbe_console.item(row, COL_ACCOUNT):
                    continue
                self.tbe_console.item(row, COL_NAME).setText(
                    cfg_account.get("player_name", "")
                )
                self.tbe_console.item(row, COL_ACCOUNT).setText(
                    decrypt(cfg_account["account"])
                )
                self.tbe_console.item(row, COL_PASSWORD).setText(
                    decrypt(cfg_account["password"])
                )
                self.tbe_console.cellWidget(row, COL_SERVER).setCurrentText(
                    cfg_account["server"]
                )

        self.cmb_arrange_get_wnd.setCurrentIndex(
            settings.cfg_common["获取窗口后排列方式"]
        )
        self.cmb_set_plan_get_wnd.setCurrentIndex(
            settings.cfg_common["获取窗口后设置方案"]
        )
        self.cmb_set_plan_db_col.setCurrentIndex(
            settings.cfg_common["双击方案列设置方案"]
        )
        self.chk_arrange_get_wnd.setChecked(settings.cfg_common["获取窗口后是否排列"])
        self.chk_set_plan_get_wnd.setChecked(
            settings.cfg_common["获取窗口后是否设置"]
        )
        self.chk_on_time.setChecked(settings.cfg_common["定时"])
        self.chk_on_time_run_all.setChecked(settings.cfg_common["定时运行全部窗口"])
        self.chk_on_time_shut_down.setChecked(settings.cfg_common["定时关闭计算机"])
        self.tmedt_on_time_run_all.setTime(QTime.fromString(settings.cfg_common["定时运行全部窗口时间"], "HH:mm"))
        self.tmedt_on_time_shut_down.setTime(QTime.fromString(settings.cfg_common["定时关闭计算机时间"], "HH:mm"))
        self.chk_hide_game_task_bar_icon.setChecked(settings.cfg_common["隐藏游戏任务栏图标"])
        self.cmb_background.setCurrentIndex(settings.cfg_common["软件背景"])
        self.slider_opaticy.setValue(settings.cfg_common["背景透明度"])
        self.edt_game_path.setText(settings.cfg_common["游戏路径"])
        self.cmb_game_server.setCurrentText(settings.cfg_common["区服"])
        self.action_boss_key.setChecked(settings.cfg_common["启用鼠标中键"])
        settings.card_number = decrypt(settings.cfg_common["卡密"])
        self.exec_rate_slider.setValue(settings.cfg_common["执行速率"] if settings.cfg_common["执行速率"] < 150 else 150)
        self.edt_card_key.setText(settings.card_number)
        self.read_price_table()
        self.resize(QSize(settings.cfg_common["窗口宽"], settings.cfg_common["窗口高"]))
        self.edt_duiyuan_run.setCursorPosition(0)
        if path_exist(settings.cfg_common["背景图片路径"]):
            self.back_img = settings.cfg_common["背景图片路径"]
            self.edt_back_img_path.setText(settings.cfg_common["背景图片路径"])
            self.update()
        self.initialed = True
        
    # 保存配置（控件到文件）
    def common_cfg_save(self, show_log=True):
        if show_log:
            self.show_info("开始保存通用配置...")
        # 基本配置
        settings.card_number = self.edt_card_key.text()
        settings.cfg_common["卡密"] = (
            encrypt(settings.card_number)
        )
        # print(f"card_number:{settings.card_number}, machine_code:{settings.machine_code}, card_rights:{settings.card_rights}")
        check_sum = get_check_sum(str(settings.card_rights), settings.card_number + settings.machine_code)
        # print(f"check_sum:{check_sum}")
        settings.cfg_common["区服"] = self.cmb_game_server.currentText()
        print(f"保存区服:{self.cmb_game_server.currentText()}")
        settings.cfg_common["MD5"] = check_sum
        if settings.due_ts > 0:
            settings.cfg_common["HMAC"] = encrypt(str(settings.due_ts))
        settings.cfg_common["获取窗口后排列方式"] = (
            self.cmb_arrange_get_wnd.currentIndex()
        )
        settings.cfg_common["获取窗口后设置方案"] = (
            self.cmb_set_plan_get_wnd.currentIndex()
        )
        settings.cfg_common["双击方案列设置方案"] = (
            self.cmb_set_plan_db_col.currentIndex()
        )
        settings.cfg_common["游戏路径"] = self.edt_game_path.text()
        settings.cfg_common["获取窗口后是否排列"] = self.chk_arrange_get_wnd.isChecked()
        settings.cfg_common["获取窗口后是否设置"] = self.chk_set_plan_get_wnd.isChecked()
        settings.cfg_common["定时"] = self.chk_on_time.isChecked()
        settings.cfg_common["定时运行全部窗口"] = self.chk_on_time_run_all.isChecked()
        settings.cfg_common["定时关闭计算机"] = self.chk_on_time_shut_down.isChecked()
        settings.cfg_common["定时运行全部窗口时间"] = self.tmedt_on_time_run_all.time().toString("HH:mm")
        settings.cfg_common["定时关闭计算机时间"] = self.tmedt_on_time_shut_down.time().toString("HH:mm")
        settings.cfg_common["隐藏游戏任务栏图标"] = self.chk_hide_game_task_bar_icon.isChecked()
        settings.cfg_common["软件背景"] = self.cmb_background.currentIndex()
        settings.cfg_common["背景透明度"] = self.slider_opaticy.value()
        settings.cfg_common["启用鼠标中键"] = self.action_boss_key.isChecked()
        settings.cfg_common["执行速率"] = self.exec_rate_slider.value()
        settings.cfg_common["游戏账号信息"] = []
        back_img_path = self.edt_back_img_path.text()
        if path_exist(back_img_path):
            settings.cfg_common["背景图片路径"] = back_img_path
            self.back_img = self.edt_back_img_path.text()
        else:
            settings.cfg_common["背景图片路径"] = ""
            self.back_img = ""
        # 最后写入到文件
        dict_to_json_file(settings.cfg_common, PATH_SOFTWARE_COMMON)
        # 单独把账密保存到另外的文件
        for row in range(self.tbe_console.rowCount()):
            item_account = self.tbe_console.item(row, COL_ACCOUNT).text()
            if not item_account:
                continue
            item_name = self.tbe_console.item(row, COL_NAME).text()
            item_password = self.tbe_console.item(row, COL_PASSWORD).text()
            item_server = self.tbe_console.cellWidget(
                row, COL_SERVER).currentText()
            settings.cfg_common["游戏账号信息"].append({
                "row": row,
                "player_name": item_name,
                "account": item_account,
                "password": item_password,
                "server": item_server,
            })
        account_infos = json.dumps(settings.cfg_common["游戏账号信息"])
        with open("config/account_infos.xdtop", "wb") as f:
            pickle.dump(encrypt(account_infos), f)
        
        if show_log:
            self.show_info("通用配置保存完成")
        self.update()

    def cmb_add_item(self, cmb: QComboBox, item_name: str, add_empty=False):
        if add_empty and cmb.count() == 0:
            cmb.addItem("")
        if cmb.findText(item_name) == -1:
            ori_index = cmb.currentIndex() if cmb.currentIndex() >= 0 else 0
            cmb.addItem(item_name)  # 添加到方案下拉列表中
            cmb.setCurrentIndex(ori_index)

    def plan_cfg_read(self):  # 文件 -> 控件
        dir_create(PATH_SOFTWARE_CONFIG)
        self.cmb_set_plan_db_col.blockSignals(True)
        self.cmb_set_plan_get_wnd.blockSignals(True)
        
        # 先把空方案设置到各个cmb_plan中
        self.cmb_set_plan_db_col.addItem("")  # 添加到 双击方案列下拉框 中
        self.cmb_set_plan_get_wnd.addItem("")  # 添加到 获取窗口后设置方案下拉框 中
        # 读取所有方案
        settings.cfg_plan_dict = json_file_to_dict(PATH_SOFTWARE_PLAN)
        # 若一个方案都没有, 帮他添加一个
        if not settings.cfg_plan_dict:
            settings.cfg_plan_dict["1免费功能"] = {
                "执行列表": ["清理背包", "签到祥瑞", "节日礼品", "四象洗炼", "跑动遇敌", "随从打书", "上架寄售", "侠义礼券"],
            }
            settings.cfg_plan_dict["2单人任务"] = {
                "执行列表": ["内功闭关", "经脉贯通", "四象跑环", "单人刷野"],
            }
            settings.cfg_plan_dict["3队长任务"] = {
                "执行列表": ["带队平乱", "带队威望", "带队刷野", "购买物品", "带队打虎", "带队打酒"],
            }
            settings.cfg_plan_dict["4队员专用"] = {
                "执行列表": ["队员挂机"],
            }
            
        # 每一个方案 都应该先设置为默认方案，再用读取到的配置来更新
        for plan_name, plan_setting_dict in settings.cfg_plan_dict.items():
            settings.cfg_plan_dict[plan_name] = copy.deepcopy(
                settings.default_cfg_plan
            )
            settings.cfg_plan_dict[plan_name].update(
                plan_setting_dict
            )
        for plan_name, _ in settings.cfg_plan_dict.items():
            self.show_info(f"读取方案 {plan_name}...")
            self.lst_plan.addItem(plan_name)  # 添加到 方案列表 中
            self.cmb_set_plan_db_col.addItem(plan_name)  # 添加到 双击方案列下拉框 中
            self.cmb_set_plan_get_wnd.addItem(plan_name)  # 添加到 获取窗口后设置方案下拉框 中
            # 添加到方案下拉列表中
            for cmb_plan in settings.cmb_plan_list:
                self.cmb_add_item(cmb_plan, plan_name, add_empty=True)
                
        self.cmb_set_plan_db_col.blockSignals(False)
        self.cmb_set_plan_get_wnd.blockSignals(False)
        self.cmb_set_plan_db_col.setView(QListView())
        self.cmb_set_plan_db_col.setStyleSheet("QComboBox QAbstractItemView::item{height:18px;}")
        self.cmb_set_plan_get_wnd.setView(QListView())
        self.cmb_set_plan_get_wnd.setStyleSheet("QComboBox QAbstractItemView::item{height:18px;}")

    def plan_cfg_save(self):  # 控件 -> 文件
        cur_item = self.lst_plan.currentItem()
        plan_name = cur_item.text()
        checks = [
            self.lst_exec.item(idx).isChecked() for idx in range(self.lst_exec.count())
        ]
        if not any(checks):
            TimeMsgBox("提示", "保存失败!方案的执行列表至少要勾选一个任务!", parent=self).exec_()
            return
        plans = [self.lst_plan.item(i).text() for i in range(self.lst_plan.count())]
        msg_box = WndPlanConfirm(plans, parent=self)
        msg_box.cmb_plan_list.setCurrentText(plan_name)
        msg_box.exec_()
        if msg_box.clickedButton() != msg_box.btn_confirm:
            return
        plan_name = msg_box.cmb_plan_list.currentText()
        print('选中的plan_name:', plan_name)
        # 保存基本配置
        settings.cfg_plan_dict[plan_name] = copy.deepcopy(
            settings.default_cfg_plan)
        settings.cfg_plan_dict[plan_name].update(
            self.get_basic_plan_setting_dict())
        # 保存各个业务任务的配置
        self.save_biz_task_setting_dict_from_control(plan_name)
        # 保存到文件
        dict_to_json_file(settings.cfg_plan_dict, PATH_SOFTWARE_PLAN)
        # 刷新所有cmb_plan的toolTip
        for row, cmb_plan in enumerate(settings.cmb_plan_list):
            if cmb_plan.currentText() == plan_name:
                wk = settings.worker_list[row]
                if wk is None:
                    continue
                self.update_worker_plan_info(wk, row, plan_name)
        # 再保存一下物价表
        self.save_price_table()
        self.show_info(f"方案配置-{plan_name} 保存成功")
        
    def save_price_table(self):
        # 逐行扫描物价表
        price_list = []
        price_dict = {}
        for row in range(self.tbe_price.rowCount()):
            name = self.tbe_price.item(row, COL_GOODS_NAME).text()
            if not name:
                break
            price = self.tbe_price.item(row, COL_GOODS_PRICE).text()
            price = int(price) if price.isdigit() else 0
            price_list.append({
                '名称': name,
                '价格': price,
                '备注': self.tbe_price.item(row, COL_GOODS_COMMENT).text(),
            })
            price_dict[name] = price
        # 保存物价表
        settings.cfg_common["物价表"] = price_list
        settings.cfg_common["商品名称价格表"] = price_dict
        dict_to_json_file(settings.cfg_common, PATH_SOFTWARE_COMMON)
        
    def read_price_table(self):
        price_list = settings.cfg_common.get("物价表", [])
        for row, item in enumerate(price_list):
            self.tbe_price.item(row, COL_GOODS_NAME).setText(item["名称"])
            self.tbe_price.item(row, COL_GOODS_PRICE).setText(str(item["价格"]))
            self.tbe_price.item(row, COL_GOODS_COMMENT).setText(item["备注"])

    def init_custom_sig_slot(self):
        self.sig_cell.connect(
            lambda row, col, info: self.tbe_console.item(
                row, col).setText(info)
        )
        self.sig_info.connect(lambda info: self.show_info(info))
        self.sig_close.connect(lambda: self.close())
        self.sig_arrange.connect(lambda: self.after_get_wnd_arrange())
        self.sig_rights.connect(
            lambda rights, is_free: self.set_card_rights(rights, is_free))
        self.sig_title.connect(lambda title: self.setWindowTitle(title))
        self.sig_remove.connect(lambda wk: self.rmv_wnd_from_console(wk))
        self.sig_account.connect(lambda cfg_account: self.on_sig_account(cfg_account))

    def init_instance_field(self):
        # 服务器列表
        self.server_list = ["默认", "一线", "二线", "三线", "四线", "五线", "六线"]
        # 背景图片
        self.back_img = ""
        # 定时器
        self.timer_cur = QTimer()  # 每隔1秒获取当前时间格式串
        self.timer_info = QTimer()  # 每隔10秒刷新小贴士信息
        self.timer_ds = QTimer()  # 每隔60秒检查是否到启动或定时的时间
        # info标签
        self.lbe_1 = QLabel("<提示>: ")
        self.lbe_info = QLabel("窗口初始化完成")
        # 执行速率标签
        self.lbe_2 = QLabel("执行速率:")
        self.lbe_exec_rate = QLabel("1.0")
        self.lbe_2.setToolTip("执行速率过快会导致部分任务异常, \n如有异常请切回1.0速率!!!")
        # 滑动条
        self.exec_rate_slider = QSlider(Qt.Horizontal)
        self.exec_rate_slider.setMinimum(50)
        self.exec_rate_slider.setMaximum(150)
        self.exec_rate_slider.setSingleStep(10)
        self.exec_rate_slider.setValue(100)
        self.exec_rate_slider.setTickPosition(QSlider.TicksAbove)
        self.exec_rate_slider.setTickInterval(20)  # 设置刻度间隔
        self.exec_rate_slider.setFixedWidth(80)
        # 按钮
        self.btn_cfg_save = QPushButton("保  存")
        self.btn_cfg_save.setFixedWidth(60)
        self.btn_cfg_save.setFixedHeight(22)
        # 日志窗口
        self.diag = QDialog(self)
        self.diag.setWindowFlags(
            self.diag.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        self.tbr_log = QTextBrowser(self.diag)
        self.form_lot = QHBoxLayout(self.diag)
        # 更新窗口
        self.wnd_update = WndUpdateSoftware(parent=self, 
                                            client_version=CLIENT_VERSION)

    def init_widgets(self):
        self.tbe_switch_account.verticalHeader().setSectionResizeMode(QHeaderView.Fixed)
        # ------------------------- 日志窗口 -------------------------
        self.diag.resize(460, 480)
        self.form_lot.addWidget(self.tbr_log)
        self.form_lot.setMargin(4)
        # ------------------------- 树列表 -------------------------
        if os.getenv("LOCAL_YT_DEBUG") == "1":
            top_level_item = QTreeWidgetItem()
            top_level_item.setText(0, "测试专用")
            self.tre_all.addTopLevelItem(top_level_item)
        self.tre_all.expandAll()
        # ------------------------- 方案列表 -------------------------
        self.lst_plan.__class__ = CustomListWidget
        # ------------------------- 选项卡 -------------------------
        self.tab_set.setCurrentIndex(0)
        # ------------------------- 状态栏 -------------------------
        self.status_bar.addWidget(self.lbe_1)
        self.status_bar.addWidget(self.lbe_info)
        self.status_bar.addPermanentWidget(self.lbe_2)
        self.status_bar.addPermanentWidget(self.lbe_exec_rate)
        self.status_bar.addPermanentWidget(self.exec_rate_slider)
        self.status_bar.addPermanentWidget(self.btn_cfg_save)
        # ------------------------- 托盘区 -------------------------
        self.tray_icon = QSystemTrayIcon(QIcon(":/xxx.ico"), parent=self)
        self.tray_icon.show()
        self.tray_icon.setToolTip("左键点我显示软件, 右键点我有菜单")
        # ------------------------- 列表框 -------------------------
        self.lst_exec.__class__ = CheckableListWidget
        # ------------------------- 表 格 -------------------------
        self.tbe_console.__class__ = ExcelTableWidget
        self.tbe_switch_account.__class__ = ExcelTableWidget
        self.init_tbe_console()
        self.init_tbe_price()
        # ------------------------- 下拉框 -------------------------
        self.cmb_set_plan_db_col.__class__ = AutoSortComboBox
        self.cmb_set_plan_get_wnd.__class__ = AutoSortComboBox
        # ------------------------- 编辑框 -------------------------
        # 创建一个正则表达式，只允许大写字母和数字
        regex = QRegExp("[A-Z0-9]*")
        validator = QRegExpValidator(regex, self.edt_card_key)
        self.edt_card_key.setValidator(validator)
        
        
            
    def init_tbe_console(self):
        tbe_console = self.tbe_console
        tbe_console.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.account_delegate = AccountDelegate()
        self.password_delegate = PasswordDelegate()
        tbe_console.setItemDelegateForColumn(
            COL_ACCOUNT, self.account_delegate)
        tbe_console.setItemDelegateForColumn(
            COL_PASSWORD, self.password_delegate)
        
        h_header = tbe_console.horizontalHeader()
        v_header = tbe_console.verticalHeader()
        # 显示表头
        h_header.setVisible(True)
        v_header.setVisible(True)
        # 设置表格各列宽度
        h_header.resizeSection(COL_HWND, 80)
        h_header.resizeSection(COL_NAME, 100)
        h_header.resizeSection(COL_PLAN, 80)
        h_header.resizeSection(COL_RUN, 32)
        h_header.resizeSection(COL_PAUSE, 32)
        h_header.resizeSection(COL_END, 32)
        h_header.resizeSection(COL_LOG, 300)
        h_header.resizeSection(COL_ACCOUNT, 120)
        h_header.resizeSection(COL_PASSWORD, 120)
        h_header.resizeSection(COL_SERVER, 80)
        # 逐行添加项
        for row in range(TBE_CONSOLE_ROW):
            cmb_plan, cmb_server = tbe_console.add_row_items(row)  # 现成
            settings.cmb_plan_list[row] = cmb_plan
            settings.cmb_server_list[row] = cmb_server
            for server in self.server_list:
                self.cmb_add_item(cmb_server, server)

            

    def init_tbe_price(self):
        tbe_price = self.tbe_price
        h_header = tbe_price.horizontalHeader()
        h_header.resizeSection(COL_GOODS_ONSHELF, 60)
        tbe_price.setItemDelegate(CompositeDelegate())
        for row in range(tbe_price.rowCount()):
            item = QTableWidgetItem()
            item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
            tbe_price.setItem(row, COL_GOODS_NAME, item)
            
            item = QTableWidgetItem()
            item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
            tbe_price.setItem(row, COL_GOODS_PRICE, item)
            
            item = QTableWidgetItem()
            item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
            tbe_price.setItem(row, COL_GOODS_ONSHELF, item)
            chk_on_shelf = QCheckBox("上架")
            chk_on_shelf.setStyleSheet("QCheckBox{margin-left: 8px;}")
            tbe_price.setCellWidget(row, COL_GOODS_ONSHELF, chk_on_shelf)
            
            item = QTableWidgetItem()
            item.setTextAlignment(Qt.AlignHCenter | Qt.AlignVCenter)
            tbe_price.setItem(row, COL_GOODS_COMMENT, item)
            
            color = QColor(230, 245, 255, 168)
            for col in range(TBE_PRICE_COL):
                tbe_price.item(row, col).setBackgroundColor(color)
    
    def init_menus(self):
        # ------------------- tbe_console设置右键菜单 -------------------
        self.tbe_console.setContextMenuPolicy(Qt.CustomContextMenu)
        self.menu_tbe_console = QMenu()
        self.action_five_game_control = QAction("一键控五")
        # -------
        self.action_get_all_wnd = QAction("获取所有窗口")
        self.action_arrange_all_wnd = QAction("排列所有窗口")  # 有二级菜单
        # -------
        self.action_login_sel_wnd = QAction("登录选中窗口")
        self.action_hide_show_sel_wnd = QAction("隐藏/显示选中窗口")
        self.action_clear_sel_wnd = QAction("清除选中窗口")
        self.action_force_exit_sel_wnd = QAction("强制退出选中窗口")
        self.menu_tbe_console.addAction(self.action_five_game_control)
        self.menu_tbe_console.addSeparator()
        self.menu_tbe_console.addAction(self.action_get_all_wnd)
        self.menu_tbe_console.addAction(self.action_arrange_all_wnd)
        self.menu_tbe_console.addSeparator()
        self.menu_tbe_console.addAction(self.action_login_sel_wnd)
        self.menu_tbe_console.addAction(self.action_hide_show_sel_wnd)
        self.menu_tbe_console.addAction(self.action_clear_sel_wnd)
        self.menu_tbe_console.addAction(self.action_force_exit_sel_wnd)
        # ----------------------- 添加二级菜单 -----------------------
        self.sub_menu_arrange_wnd = QMenu()
        self.sub_action_method0 = QAction("0垂直紧密")
        self.sub_action_method1 = QAction("1垂直扩展")
        self.sub_action_method2 = QAction("2平铺4K")
        self.sub_action_method3 = QAction("3对角紧密")
        self.sub_action_method4 = QAction("4左上重叠")
        self.sub_menu_arrange_wnd.addActions(
            [
                self.sub_action_method0,
                self.sub_action_method1,
                self.sub_action_method2,
                self.sub_action_method3,
                self.sub_action_method4,
            ]
        )
        # 添加子菜单
        self.action_arrange_all_wnd.setMenu(self.sub_menu_arrange_wnd)
        
        self.tbe_console.customContextMenuRequested.connect(
            self.on_custom_context_menu_requested
        )
        # 连接信号槽
        self.action_five_game_control.triggered.connect(
            self.on_action_five_game_control_triggered
        )
        self.action_get_all_wnd.triggered.connect(
            self.one_key_get_wnd
        )
        self.action_clear_sel_wnd.triggered.connect(
            self.on_action_clear_sel_wnd_triggered
        )
        self.action_hide_show_sel_wnd.triggered.connect(
            self.on_action_hide_show_sel_wnd_triggered
        )
        self.action_force_exit_sel_wnd.triggered.connect(
            self.on_action_force_exit_sel_wnd_triggered
        )
        self.action_login_sel_wnd.triggered.connect(
            self.on_action_login_sel_wnd_triggered
        )

        # 子菜单action信号槽
        self.sub_action_method0.triggered.connect(
            lambda: arrange_all_wnd(
                0, W_GAME, H_GAME, settings.SCREEN_W, settings.SCREEN_H)
        )
        self.sub_action_method1.triggered.connect(
            lambda: arrange_all_wnd(
                1, W_GAME, H_GAME, settings.SCREEN_W, settings.SCREEN_H)
        )
        self.sub_action_method2.triggered.connect(
            lambda: arrange_all_wnd(
                2, W_GAME, H_GAME, settings.SCREEN_W, settings.SCREEN_H)
        )
        self.sub_action_method3.triggered.connect(
            lambda: arrange_all_wnd(
                3, W_GAME, H_GAME, settings.SCREEN_W, settings.SCREEN_H)
        )
        self.sub_action_method4.triggered.connect(
            lambda: arrange_all_wnd(
                4, W_GAME, H_GAME, settings.SCREEN_W, settings.SCREEN_H)
        )

        # ------------------- lst_exec设置右键菜单 -------------------
        self.lst_exec.setContextMenuPolicy(Qt.CustomContextMenu)
        self.menu_lst_exec = QMenu()
        self.action_lst_exec_clear = QAction("清空执行列表(&C)")
        self.action_copy_exec_list = QAction("复制执行列表")
        self.action_paste_exec_list = QAction("粘贴执行列表")
        self.action_paste_exec_list.setEnabled(False)
        self.action_append_func = QAction("追加选中功能")
        self.action_del_func = QAction("删除选中功能")
        self.action_cancel_func = QAction("取消选中所有")
        # 添加菜单项
        self.menu_lst_exec.addAction(self.action_lst_exec_clear)
        self.menu_lst_exec.addAction(self.action_copy_exec_list)
        self.menu_lst_exec.addAction(self.action_paste_exec_list)
        self.menu_lst_exec.addSeparator()
        self.menu_lst_exec.addAction(self.action_append_func)
        self.menu_lst_exec.addAction(self.action_del_func)
        self.menu_lst_exec.addSeparator()
        self.menu_lst_exec.addAction(self.action_cancel_func)
        # 连接信号槽
        self.lst_exec.customContextMenuRequested.connect(
            lambda: self.menu_lst_exec.exec_(QCursor.pos())
        )
        self.action_lst_exec_clear.triggered.connect(
            lambda: self.lst_exec.clear())
        self.action_copy_exec_list.triggered.connect(
            self.on_action_copy_exec_list_triggered)
        self.action_paste_exec_list.triggered.connect(
            self.on_action_paste_exec_list_triggered)
        self.action_append_func.triggered.connect(
            self.on_action_append_func_triggered)
        self.action_del_func.triggered.connect(
            self.on_action_del_func_triggered)
        self.action_cancel_func.triggered.connect(
            self.on_action_cancel_func_triggered)
        # ------------------- tray_icon设置右键菜单 -------------------
        self.menu_tray_icon = QMenu()
        # 创建菜单项
        self.action_kill_all_wnd = QAction("强制退出所有游戏窗口(&K)")
        self.action_hide_show_all_wnd = QAction("隐藏/显示所有游戏窗口(&H)")
        self.action_hide_show_icon = QAction("隐藏/显示任务栏图标(&I)")
        self.action_boss_key = QAction("启用鼠标中键显示/隐藏")
        self.action_quit = QAction("退出本软件(&Q)")
        # 菜单项额外设置
        self.action_boss_key.setCheckable(True)
        self.action_boss_key.setChecked(True)
        # 添加菜单项
        self.menu_tray_icon.addAction(self.action_kill_all_wnd)
        self.menu_tray_icon.addSeparator()
        self.menu_tray_icon.addAction(self.action_hide_show_all_wnd)
        self.menu_tray_icon.addAction(self.action_hide_show_icon)
        self.menu_tray_icon.addAction(self.action_boss_key)
        self.menu_tray_icon.addSeparator()
        self.menu_tray_icon.addAction(self.action_quit)
        # 为图标设置菜单
        self.tray_icon.setContextMenu(self.menu_tray_icon)
        # 连接信号槽
        self.tray_icon.activated.connect(self.on_tray_icon_activated)
        self.action_kill_all_wnd.triggered.connect(
            self.on_action_kill_all_wnd_triggered
        )
        self.action_hide_show_all_wnd.triggered.connect(
            self.on_action_hide_show_all_wnd_triggered
        )
        self.action_hide_show_icon.triggered.connect(
            self.on_action_hide_show_icon_triggered
        )
        self.action_boss_key.toggled.connect(
            self.on_action_boss_key_toggled
        )
        self.action_quit.triggered.connect(self.on_action_quit_triggered)

        # ------------------- lst_plan设置右键菜单 -------------------
        self.lst_plan.setContextMenuPolicy(Qt.CustomContextMenu)
        self.menu_lst_plan = QMenu()
        self.action_lst_plan_del = QAction("删除此方案(&D)")
        self.action_lst_plan_cancel = QAction("取消选中")
        # 添加菜单项
        self.menu_lst_plan.addAction(self.action_lst_plan_del)
        self.menu_lst_plan.addAction(self.action_lst_plan_cancel)
        self.lst_plan.customContextMenuRequested.connect(
            lambda: self.menu_lst_plan.exec_(QCursor.pos())
        )
        self.action_lst_plan_del.triggered.connect(
            self.on_action_lst_plan_del_triggered
        )
        self.action_lst_plan_cancel.triggered.connect(
            lambda: self.lst_plan.setCurrentItem(None)
        )

    def init_sig_slot(self):
        self.edt_func_search.textChanged.connect(
            self.on_edt_func_search_textChanged
        )
        self.edt_func_search.returnPressed.connect(
            self.on_edt_func_search_returnPressed
        )
        self.cmb_background.currentIndexChanged.connect(
            self.on_cmb_background_currentIndexChanged
        )
        self.slider_opaticy.valueChanged.connect(
            self.on_slider_opaticy_valueChanged
        )
        # ------------------- 窗口 -------------------
        self.wnd_update.sig_update_finish.connect(
            self.on_wnd_update_sig_update_finish
        )
        # ------------------- 时钟 -------------------
        self.timer_cur.timeout.connect(self.on_timer_cur_timeout)
        self.timer_cur.start(1000)
        self.timer_info.timeout.connect(self.on_timer_info_timeout)
        self.timer_info.start(1000 * 10)
        self.timer_ds.timeout.connect(self.on_timer_ds_timeout)
        self.timer_ds.start(1000 * 60)
        # ------------------- 工具栏 -------------------
        self.tool_bar.actionTriggered.connect(self.on_tool_bar_actionTriggered)
        self.tool_bar.orientationChanged.connect(
            self.on_tool_bar_orientationChanged)

        self.chk_hide_game_task_bar_icon.stateChanged.connect(self.on_chk_hide_game_task_bar_icon_stateChanged)
        # ------------------- 卡密相关 -------------------
        self.btn_card_use.clicked.connect(self.on_btn_card_use_clicked)
        self.btn_card_unbind.clicked.connect(self.on_btn_card_unbind_clicked)
        self.btn_official.clicked.connect(self.on_btn_official_clicked)
        # ------------------- 检查更新 -------------------
        self.btn_check_update.clicked.connect(self.on_btn_check_update_clicked)
        # ------------------- 中控台表格 -------------------
        h_header = self.tbe_console.horizontalHeader()
        v_header = self.tbe_console.verticalHeader()
        h_header.sectionDoubleClicked.connect(self.on_h_header_double_clicked)
        v_header.sectionDoubleClicked.connect(self.on_v_header_double_clicked)
        self.tbe_console.cellClicked.connect(self.on_tbe_console_cellClicked)
        self.tbe_console.cellDoubleClicked.connect(
            self.on_tbe_console_cellDoubleClicked
        )
        self.tbe_console.itemChanged.connect(self.on_tbe_console_itemChanged)
        for cmb_plan in settings.cmb_plan_list:
            cmb_plan.currentTextChanged.connect(self.on_cmb_plan_cur_text_changed)
        # ------------------- 其它表格 -----------------------
        self.tbe_switch_account.horizontalHeader().setVisible(True)
        self.tbe_switch_account.verticalHeader().setVisible(True)
        # ------------------- 选项卡-设置 -------------------
        self.tab_set.currentChanged.connect(self.on_tab_set_currentChanged)
        # ------------------- 方案设置 -------------------
        # tre_all双击添加
        self.tre_all.itemDoubleClicked.connect(
            self.on_tre_all_item_double_clicked)
        # lst_exec双击删除
        self.lst_exec.itemDoubleClicked.connect(
            self.on_lst_exec_item_double_clicked)
        # lst_plan单击读取配置
        self.lst_plan.itemClicked.connect(
            self.on_lst_plan_item_clicked)
        # lst_plan当前项改变时设置编辑框内容
        self.lst_plan.currentTextChanged.connect(
            self.on_lst_plan_currentTextChanged)
        # btn_plan_create点击新建配置文件
        self.btn_plan_create.clicked.connect(self.on_btn_plan_create_clicked)
        # btn_plan_rename点击重命名配置文件
        self.btn_plan_rename.clicked.connect(self.on_btn_plan_rename_clicked)
        # chk_shuaye_terse_bb弹窗
        self.chk_shuaye_terse_bb.stateChanged.connect(
            self.on_chk_shuaye_terse_bb_stateChanged
        )
        self.tbtn_custom_img.clicked.connect(self.on_tbtn_custom_img_clicked)
        # 业务任务说明
        self.chk_deliver_yxb.stateChanged.connect(
            self.on_chk_deliver_yxb_stateChanged
        )
        self.btn_ji_he.clicked.connect(self.on_btn_ji_he_clicked)
        self.btn_ding_shi.clicked.connect(self.on_btn_ding_shi_clicked)
        self.btn_bb_skill.clicked.connect(self.on_btn_bb_skill_clicked)
        self.btn_fu_ben_cou_shu.clicked.connect(self.on_btn_fu_ben_cou_shu_clicked)
        self.btn_jin_zheng.clicked.connect(self.on_btn_jin_zheng_clicked)
        self.btn_back_city_team.clicked.connect(
            self.on_btn_back_city_team_clicked)
        self.btn_back_city_single.clicked.connect(
            self.on_btn_back_city_single_clicked)
        self.btn_lead_team_line.clicked.connect(
            self.on_btn_lead_team_line_clicked)
        self.btn_auto_skill.clicked.connect(
            self.on_btn_auto_skill_clicked)
        self.btn_qi_hao.clicked.connect(
            self.on_btn_qi_hao_clicked)
        self.btn_jiao_tao_zi.clicked.connect(
            self.on_btn_jiao_tao_zi_clicked)
        self.btn_bang_hui.clicked.connect(
            self.on_btn_bang_hui_clicked)
        self.btn_kai_bao_he.clicked.connect(
            self.on_btn_kai_bao_he_clicked)
        self.btn_meng_jing.clicked.connect(
            self.on_btn_meng_jing_clicked)
        self.btn_switch_primary_bb.clicked.connect(
            self.on_btn_switch_primary_bb_clicked)
        self.btn_call_bb.clicked.connect(
            self.on_btn_call_bb_clicked)
        self.btn_chuang_guan.clicked.connect(
            self.on_btn_chuang_guan_clicked)
        self.btn_people_use_skill.clicked.connect(
            self.on_btn_people_use_skill_clicked)
        self.btn_listen_add.clicked.connect(
            self.on_btn_listen_add_clicked
        )
        self.btn_listen_history.clicked.connect(
            self.on_btn_listen_history_clicked
        )
        self.lst_listen_world.itemDoubleClicked.connect(
            self.on_lst_listen_world_item_double_clicked
        )
        # ------------------- 通用设置 -------------------
        self.cmb_set_plan_db_col.currentIndexChanged.connect(
            self.on_cmb_set_plan_db_col_cur_index_changed
        )
        self.cmb_arrange_get_wnd.currentIndexChanged.connect(
            self.on_cmb_arrange_get_wnd_cur_index_changed
        )
        self.cmb_set_plan_get_wnd.currentIndexChanged.connect(
            self.on_cmb_set_plan_get_wnd_cur_index_changed
        )
        self.btn_cfg_save.clicked.connect(self.on_btn_cfg_save_clicked)
        self.btn_start_stop_login.clicked.connect(
            self.on_btn_start_stop_login_clicked)

        self.btn_open_folder_game_path.clicked.connect(
            self.on_btn_open_folder_game_path_clicked
        )
        self.btn_open_game.clicked.connect(
            self.on_btn_open_game_clicked
        )
        self.exec_rate_slider.valueChanged.connect(
            self.on_exec_rate_slider_value_changed
        )
        self.edt_duiyuan_run.editingFinished.connect(
            lambda: self.edt_duiyuan_run.setCursorPosition(0)
        )

    def show_info(self, info: str):
        self.lbe_info.setText(info)
        log.info(info)

    def show_tip(self, tip: str):
        self.lbe_info.setText(tip)

    def get_basic_plan_setting_dict(self):
        return {
            # 人物战斗
            "人物战斗救人": self.groupBox_people_save_people.isChecked(),
            "人物战斗救人比例": self.spin_count_people_save_people.value(),
            "人物战斗救BB": self.groupBox_people_save_bb.isChecked(),
            "人物战斗救BB比例": self.spin_count_people_save_bb.value(),
            "人物使用技能": self.groupBox_people_use_skill.isChecked(),
            "人物技能使用频率": self.spin_count_skill_frequency.value(),
            "人物护心百分比": self.spin_count_huxin_percent.value(),
            "人物未命中继续刺": self.chk_people_skill_continue_cixue.isChecked(),
            "人物刺怪比例": self.spin_count_people_skill_continue_cixue.value(),
            "人物刺穴F7": self.chk_people_skill_cixue.isChecked(),
            "人物护心F6": self.chk_people_skill_huxin.isChecked(),
            "人物五行克制F1": self.chk_people_skill_single.isChecked(),
            "人物战斗": get_checked_radio_text_in_groupbox(self.groupBox_people),
            "人物战后补充": self.chk_after_fight_people_supply.isChecked(),
            "人物战后补充比例": self.spin_count_after_fight_people_supply.value(),
            "人物切回首发BB": self.chk_people_switch_primary_bb.isChecked(),
            "唤出BB": self.groupBox_call_bb.isChecked(),
            "唤出BB血内比例": self.spin_count_call_bb_blood.value(),
            "唤出BB回合数": self.spin_count_call_bb_round.value(),
            "唤出BB名字": self.edt_call_bb_name.text(),
            # BB战斗
            "进攻类BB战斗救人": self.groupBox_fight_bb_save_people.isChecked(),
            "进攻类BB战斗救人比例": self.spin_count_fight_bb_save_people.value(),
            "进攻类BB战斗救BB": self.groupBox_fight_bb_save_bb.isChecked(),
            "进攻类BB战斗救BB比例": self.spin_count_fight_bb_save_bb.value(),
            "防守类BB战斗救人": self.groupBox_defend_bb_save_people.isChecked(),
            "防守类BB战斗救人比例": self.spin_count_defend_bb_save_people.value(),
            "防守类BB战斗救BB": self.groupBox_defend_bb_save_bb.isChecked(),
            "防守类BB战斗救BB比例": self.spin_count_defend_bb_save_bb.value(),
            "BB使用技能": self.groupBox_bb_use_skill.isChecked(),
            "BB厚积": self.chk_fight_bb_hj.isChecked(),
            "BB激将": self.chk_fight_bb_jj.isChecked(),
            "BB破釜": self.chk_fight_bb_pf.isChecked(),
            "BB乱神隔世": self.chk_fight_bb_lsgs.isChecked(),
            "BB金钟罩": self.chk_fight_bb_jzz.isChecked(),
            "BB铁布衫": self.chk_fight_bb_tbs.isChecked(),
            "BB破釜回合数": self.spin_count_fight_bb_pf_round.value(),
            # 战斗通用配置
            "人物救人宠前提": self.groupBox_fight_save_enemy_less.isChecked(),
            "人物拉药怪数": self.spin_count_fight_save_enemy_less.value(),
            "BB救人宠前提": self.groupBox_fight_save_enemy_less_bb.isChecked(),
            "BB拉药怪数": self.spin_count_fight_save_enemy_less_bb.value(),
            # 其它
            "执行列表": [
                self.lst_exec.item(idx).text() for idx in range(self.lst_exec.count())
            ],
            "执行列表选中": [
                self.lst_exec.item(idx).isChecked() for idx in range(self.lst_exec.count())
            ],
            "出城": get_checked_radio_text_in_groupbox(self.groupBox_go),
            "回城": get_checked_radio_text_in_groupbox(self.groupBox_back),
            "BB攻防": get_checked_radio_text_in_groupbox(self.groupBox_bb_fight_defend),
            "人宠使用技能": self.cmb_people_bb_use_skill.currentText(),
            "查找灰色行囊页": self.chk_search_gray_page.isChecked(),
            "点击整理": self.chk_click_tidy_up.isChecked(),
            "换副本前修理忠诚": self.chk_fuben_switch_fix_bb.isChecked(),
            "修理忠诚方式": get_checked_radio_text_in_groupbox(self.groupBox_fix_bb),
            "带队换线": self.chk_lead_team_line.isChecked(),
            "领双带队打牛": self.chk_daniu_ls.isChecked(),
            "领双时间带队打牛": self.cmb_daniu_double_time.currentText(),
            "领双围捕大盗": self.chk_weibu_ls.isChecked(),
            "领双时间围捕大盗": self.cmb_weibu_double_time.currentText(),
            "领双边关清剿": self.chk_bianguan_ls.isChecked(),
            "领双时间边关清剿": self.cmb_bianguan_double_time.currentText(),
            "领双百战百胜": self.chk_baizhan_ls.isChecked(),
            "领双时间百战百胜": self.cmb_baizhan_double_time.currentText(),
            "组满第五页": self.chk_make_team_full.isChecked(),
        }

    def read_basic_plan_setting_dict_to_control(self, update_plan_setting_dict: dict):
        if update_plan_setting_dict is None:
            return
        plan_setting_dict = copy.deepcopy(settings.default_cfg_plan)
        plan_setting_dict.update(update_plan_setting_dict)
        # 人物战斗
        self.groupBox_people_save_people.setChecked(
            plan_setting_dict["人物战斗救人"])
        self.spin_count_people_save_people.setValue(
            plan_setting_dict["人物战斗救人比例"]
        )
        self.groupBox_people_save_bb.setChecked(plan_setting_dict["人物战斗救BB"])
        self.spin_count_people_save_bb.setValue(plan_setting_dict["人物战斗救BB比例"])
        self.groupBox_people_use_skill.setChecked(plan_setting_dict["人物使用技能"])
        self.spin_count_skill_frequency.setValue(plan_setting_dict["人物技能使用频率"])
        self.spin_count_huxin_percent.setValue(plan_setting_dict["人物护心百分比"])
        self.chk_people_skill_continue_cixue.setChecked(plan_setting_dict["人物未命中继续刺"])
        self.spin_count_people_skill_continue_cixue.setValue(plan_setting_dict["人物刺怪比例"])
        self.chk_people_skill_cixue.setChecked(plan_setting_dict["人物刺穴F7"])
        self.chk_people_skill_huxin.setChecked(plan_setting_dict["人物护心F6"])
        self.chk_people_skill_single.setChecked(plan_setting_dict["人物五行克制F1"])
        set_checked_radio_text_in_groupbox(
            self.groupBox_people, plan_setting_dict["人物战斗"]
        )
        self.chk_after_fight_people_supply.setChecked(
            plan_setting_dict["人物战后补充"]
        )
        self.spin_count_after_fight_people_supply.setValue(
            plan_setting_dict["人物战后补充比例"]
        )
        self.chk_people_switch_primary_bb.setChecked(
            plan_setting_dict["人物切回首发BB"])
        self.groupBox_call_bb.setChecked(plan_setting_dict["唤出BB"])
        self.spin_count_call_bb_blood.setValue(plan_setting_dict["唤出BB血内比例"])
        self.spin_count_call_bb_round.setValue(plan_setting_dict["唤出BB回合数"])
        self.edt_call_bb_name.setText(plan_setting_dict["唤出BB名字"])
        # BB战斗
        self.groupBox_fight_bb_save_people.setChecked(
            plan_setting_dict["进攻类BB战斗救人"])
        self.spin_count_fight_bb_save_people.setValue(
            plan_setting_dict["进攻类BB战斗救人比例"])
        self.groupBox_fight_bb_save_bb.setChecked(
            plan_setting_dict["进攻类BB战斗救BB"])
        self.spin_count_fight_bb_save_bb.setValue(
            plan_setting_dict["进攻类BB战斗救BB比例"])
        self.groupBox_defend_bb_save_people.setChecked(
            plan_setting_dict["防守类BB战斗救人"])
        self.spin_count_defend_bb_save_people.setValue(
            plan_setting_dict["防守类BB战斗救人比例"])
        self.groupBox_defend_bb_save_bb.setChecked(
            plan_setting_dict["防守类BB战斗救BB"])
        self.spin_count_defend_bb_save_bb.setValue(
            plan_setting_dict["防守类BB战斗救BB比例"])
        self.groupBox_bb_use_skill.setChecked(plan_setting_dict["BB使用技能"])
        self.chk_fight_bb_lsgs.setChecked(
            plan_setting_dict.get("BB乱神隔世", False)
        )
        self.chk_fight_bb_jzz.setChecked(
            plan_setting_dict.get("BB金钟罩", False)
        )
        self.chk_fight_bb_tbs.setChecked(
            plan_setting_dict.get("BB铁布衫", False)
        )
        self.chk_fight_bb_hj.setChecked(plan_setting_dict["BB厚积"])
        self.chk_fight_bb_jj.setChecked(plan_setting_dict["BB激将"])
        self.chk_fight_bb_pf.setChecked(plan_setting_dict["BB破釜"])
        self.spin_count_fight_bb_pf_round.setValue(
            plan_setting_dict["BB破釜回合数"]
        )
        # 战斗通用配置
        self.groupBox_fight_save_enemy_less.setChecked(plan_setting_dict["人物救人宠前提"])
        self.spin_count_fight_save_enemy_less.setValue(plan_setting_dict["人物拉药怪数"])
        self.groupBox_fight_save_enemy_less_bb.setChecked(plan_setting_dict["BB救人宠前提"])
        self.spin_count_fight_save_enemy_less_bb.setValue(plan_setting_dict["BB拉药怪数"])
        # 其它
        self.lst_exec.clear()
        self.lst_exec.addItems(plan_setting_dict["执行列表"])
        for row, checked in enumerate(plan_setting_dict["执行列表选中"]):
             self.lst_exec.item(row).setChecked(checked)
        set_checked_radio_text_in_groupbox(
            self.groupBox_go, plan_setting_dict["出城"])
        set_checked_radio_text_in_groupbox(
            self.groupBox_back, plan_setting_dict["回城"]
        )
        set_checked_radio_text_in_groupbox(
            self.groupBox_bb_fight_defend, plan_setting_dict["BB攻防"])
        self.cmb_people_bb_use_skill.setCurrentText(
            plan_setting_dict["人宠使用技能"])
        self.chk_search_gray_page.setChecked(plan_setting_dict["查找灰色行囊页"])
        self.chk_click_tidy_up.setChecked(plan_setting_dict["点击整理"])
        self.chk_fuben_switch_fix_bb.setChecked(plan_setting_dict["换副本前修理忠诚"])
        self.chk_lead_team_line.setChecked(plan_setting_dict["带队换线"])
        set_checked_radio_text_in_groupbox(
            self.groupBox_fix_bb, plan_setting_dict["修理忠诚方式"])
        self.chk_daniu_ls.setChecked(plan_setting_dict["领双带队打牛"])
        self.cmb_daniu_double_time.setCurrentText(plan_setting_dict["领双时间带队打牛"])
        self.chk_weibu_ls.setChecked(plan_setting_dict["领双围捕大盗"])
        self.cmb_weibu_double_time.setCurrentText(plan_setting_dict["领双时间围捕大盗"])
        self.chk_bianguan_ls.setChecked(plan_setting_dict["领双边关清剿"])
        self.cmb_bianguan_double_time.setCurrentText(plan_setting_dict["领双时间边关清剿"])
        self.chk_baizhan_ls.setChecked(plan_setting_dict["领双百战百胜"])
        self.cmb_baizhan_double_time.setCurrentText(plan_setting_dict["领双时间百战百胜"])
        self.chk_make_team_full.setChecked(plan_setting_dict["组满第五页"])

    def read_biz_task_setting_dict_to_control(self, plan_name: str):
        # 配置文件 到 当前控件
        for _, task_cls in settings.task_dict.items():
            task_cls.cfg_read(plan_name)

    def save_biz_task_setting_dict_from_control(self, plan_name: str):
        # 当前控件 到 全局变量(用来存储到配置文件)
        for _, task_cls in settings.task_dict.items():
            task_cls.cfg_save(plan_name)

    def thd_hotkey(self):
        self.show_info("鼠标监听开始...")
        from pynput import mouse

        def on_click(x, y, button, pressed):
            if not self.ENABLE_BOSS_KEY:
                return
            if not pressed:
                return
            if button != mouse.Button.middle:
                return
            hwnd = settings.com_obj.get_mouse_point_window()
            print(f"点击了中键, 坐标: {x}, {y}, 窗口句柄: {hwnd}")
            buffer_size = 128
            buffer = ctypes.create_unicode_buffer(buffer_size)
            XWinAPI.GetClassName(hwnd, buffer, buffer_size)
            wnd_class_name = buffer.value
            if wnd_class_name != WND_CLASS:
                for wk in settings.worker_list:
                    if wk is None:
                        continue
                    Thread(target=self.thd_hide_show, args=(
                        wk, self.ALL_SHOW), daemon=True).start()
                self.ALL_SHOW = not self.ALL_SHOW
                return
            for wk in settings.worker_list:
                if wk is None:
                    continue
                if wk.hwnd != hwnd:
                    continue
                if wk.is_run:  # 如果在运行，就终止
                    wk.record(f"点击了中键, 窗口终止")
                    self.tbe_console.item(wk.row, COL_END).setText(SELECTED)
                else:  # 其它情况下直接运行
                    wk.record(f"点击了中键, 窗口运行")
                    self.tbe_console.item(wk.row, COL_RUN).setText(SELECTED)
                return

        with mouse.Listener(on_click=on_click) as listener:
            listener.join()
        print("鼠标监听结束")

    def thd_heart_beat(self):
        self.heart_fail_count = 0
        self.last_heart_stamp = settings.cur_time_stamp
        while True:
            time.sleep(60*10)  # 每10分钟心跳一次
            self.last_heart_stamp = settings.cur_time_stamp
            self.send_request_heart_v2()

    def one_key_get_wnd(self):
        hwnd_list = find_windows_by_name(WND_TITLE)
        if hwnd_list == []:
            self.show_info("未发现游戏窗口,请打开游戏后再试")
            return
        log.info(f"获取到的窗口句柄:{hwnd_list}")
        # 获取 用户设定的方案索引
        plan_idx = settings.cfg_common["获取窗口后设置方案"]
        # 这个用来保存每个窗口对应的行号，要一一匹配, 如果是之前设置过的窗口, 则row设为None，后面就不再创建wk
        row_list = []
        for hwnd in hwnd_list:
            row = self.add_hwnd(hwnd)
            if row == -1:
                row_list.append(None)
            else:
                row_list.append(row)
        # 下面的操作创建一个线程来做
        self.thd_get_wnd_list = []
        for hwnd, row in zip(hwnd_list, row_list):
            if row is None:
                continue
            t = Thread(target=self.thd_create_wk_for_hwnd, args=(hwnd, row, plan_idx), daemon=True)
            t.start()
            self.thd_get_wnd_list.append(t)
        self.sig_arrange.emit()

    def add_hwnd(self, hwnd) -> int:
        # 若窗口已经存在, 则跳过下面步骤
        if hwnd in settings.hwnd_list:
            return -1
        # 找一个空位填充
        row = settings.hwnd_list.index(None)
        self.tbe_console.item(row, COL_HWND).setText(str(hwnd))
        settings.hwnd_list[row] = hwnd
        return row
    
    def locate_hwnd(self, hwnd: int, row: int):
        # 若窗口已经存在, 则跳过下面步骤
        if hwnd in settings.hwnd_list:
            return -1
        # 把窗口放到指定行
        self.tbe_console.item(row, COL_HWND).setText(str(hwnd))
        settings.hwnd_list[row] = hwnd
        return row
    
    def thd_create_wk_for_hwnd(self, hwnd, row, plan_idx=0):
        pythoncom.CoInitialize()
        # 创建com对象
        wnd_com_obj = create_com_obj()
        # 创建worker对象
        wk = Worker(hwnd, wnd_com_obj, row, TaskBase.get_team(row))
        # 把wk添加到列表中
        while len(settings.worker_list) <= row:
            settings.worker_list.append(None)
        settings.worker_list[row] = wk
        # 清空之前的日志内容
        row_num = row + 1
        file_clear_content(f"{PATH_SOFTWARE_LOG}\\wnd_{row_num}.txt")
        # 激活cmb_plan,  并设置方案
        cmb_plan = settings.cmb_plan_list[row]
        cmb_plan.setEnabled(True)
        cmb_plan.setCurrentIndex(plan_idx)
        # 获取窗口位置
        x, y = get_wnd_pos(hwnd)
        if x != HIDE_X:
            wk.x, wk.y = x, y
        wk.record(f"窗口{row_num}获取成功, 并自动设置为{plan_idx}号方案")

    def after_get_wnd_arrange(self):
        log.info("开始等待所有线程")
        if getattr(self, "thd_get_wnd_list", None):
            for thd in self.thd_get_wnd_list:
                thd.join()
            self.thd_get_wnd_list = []
        log.info("结束等待所有线程")
        # 排列窗口
        if not self.chk_arrange_get_wnd.isChecked():
            return
        log.info("开始排列窗口")
        mode = settings.cfg_common["获取窗口后排列方式"]
        print(f"排列方式：{mode}")
        arrange_all_wnd(mode, W_GAME, H_GAME,
                        settings.SCREEN_W, settings.SCREEN_H)
        self.show_info(f"获取所有游戏窗口成功, 并按方式{mode}排列")

    def set_card_rights(self, rights="One", is_free=True):
        resp_card_rights = {
            "One": 1,
            "Five": 5,
            "Ten": 10,
            "Twenty": 20,
            "Thirty": 30,
            "Sixty": 60,
        }.get(rights, 1)
        settings.card_rights = resp_card_rights
        settings.is_free = is_free
        soft_type = "免费" if is_free else "VIP"
        multi = "单" if settings.card_rights == 1 else str(settings.card_rights)
        # 发信号设置窗口标题, 防逆向
        self.sig_title.emit(f"{APP_NAME}{CLIENT_VERSION}-{multi}开{soft_type}版")

    def check_game_path(self):
        game_path = self.edt_game_path.text()
        print('game_path:', game_path)
        if (
            game_path == ""
            or not game_path.endswith("update.exe")
            or not os.path.exists(game_path)
        ):
            self.show_info("请先设置游戏路径!")
            return ""
        return game_path

    def on_btn_start_stop_login_clicked(self):
        game_path = self.check_game_path()
        if not game_path:
            return
        if self.btn_start_stop_login.text() == "开始登录":
            if getattr(self, 'game_login_thread', None) and self.game_login_thread.isRunning():
                return
            self.btn_start_stop_login.setText("停止登录")
            self.game_login_thread = ThreadLogin(game_path)
            self.game_login_thread.start()
        else:
            locker = QMutexLocker(settings.global_wk.mutex)
            self.game_login_thread.terminate()
            self.game_login_thread.wait()
            settings.global_wk.hwnd = 0
            settings.global_wk.unbind_window()
            self.btn_start_stop_login.setText("开始登录")

    def one_key_set_plan(self, idx):
        for wk in settings.worker_list:
            if wk is None:
                continue
            self.set_plan(wk.row, idx)

    def set_plan(self, row, plan_idx):
        cmb_plan = settings.cmb_plan_list[row]
        if not cmb_plan.isEnabled() or cmb_plan.count() < plan_idx:
            return
        cmb_plan.setCurrentIndex(plan_idx)

    def rmv_wnd_from_console(self, wk: Worker):
        row = wk.row
        # 先把工人停止
        if wk and not wk.is_end:
            wk.thread.end()
        # 移除此工人
        settings.worker_list[row] = None
        settings.hwnd_list[row] = None
        wk = None
        # 清空此行内容
        for col in range(COL_LOG + 1):
            if col == COL_NAME:
                continue
            item = self.tbe_console.item(row, col)
            if item:
                item.setText("")
        # 禁用cmb_plan
        cmb_plan = settings.cmb_plan_list[row]
        cmb_plan.setEnabled(False)
        cmb_plan.setCurrentIndex(-1)

    def on_sig_account(self, cfg_account: dict):
        row = cfg_account["row"]
        if not self.tbe_console.item(row, COL_ACCOUNT):
            return
        self.tbe_console.item(row, COL_NAME).setText(
            cfg_account.get("player_name", "")
        )
        self.tbe_console.item(row, COL_ACCOUNT).setText(
            cfg_account["account"]
        )
        self.tbe_console.item(row, COL_PASSWORD).setText(
            cfg_account["password"]
        )
        self.tbe_console.cellWidget(row, COL_SERVER).setCurrentText(
            cfg_account["server"]
        )

    def update_cmb_plan_tooltip(self, row):
        wk = settings.worker_list[row]
        cmb_plan = settings.cmb_plan_list[row]
        tasks = wk.cfg_plan["执行列表"]
        checks = wk.cfg_plan["执行列表选中"]
        if len(checks) == len(tasks):
            checked_tasks = []
            for i, task in enumerate(tasks):
                if checks[i]:
                    checked_tasks.append(task)
        else:
            checked_tasks = tasks
        tooltip = "-".join(checked_tasks)
        cmb_plan.setToolTip(tooltip)
        
    def get_start_row(self, sel_row):
        row_count = self.tbe_console.rowCount()
        for cur_row in range(5, row_count, 5):
            if sel_row / cur_row < 1:
                return cur_row - 5
        res = row_count - (row_count % 5)
        print(f'sel_row:{sel_row}, res:{res}, row_count:{row_count}')      
        return res

    # ---------------------- 自定义槽函数 -----------------------
    def on_custom_context_menu_requested(self):
        self.selected_rows = list(
            {index.row() for index in self.tbe_console.selectedIndexes()}
        )
        self.selected_rows.sort()
        if not self.selected_rows:
            return
        sel_row = self.selected_rows[0]
        self.start_row = self.get_start_row(sel_row)
        self.start_col = self.tbe_console.currentColumn()
        self.menu_tbe_console.exec_(QCursor.pos())

    def on_tray_icon_activated(self, reason):
        if reason != QSystemTrayIcon.Trigger:
            return
        if self.isMinimized():
            self.showNormal()
        self.activateWindow()

    ALL_SHOW = False
    def on_action_hide_show_all_wnd_triggered(self):
        # 先找出第一个不为None的窗口，决定所有的窗口是显示还是隐藏
        for wk in settings.worker_list:
            if wk is None:
                continue
            Thread(target=self.thd_hide_show, args=(
                wk, self.ALL_SHOW), daemon=True).start()
        self.ALL_SHOW = not self.ALL_SHOW

    SHOW_ICON = False
    def on_action_hide_show_icon_triggered(self):
        state = Qt.Unchecked if self.SHOW_ICON else Qt.Checked
        self.do_hide_show_task_bar_icon(state)
        self.SHOW_ICON = not self.SHOW_ICON

    ENABLE_BOSS_KEY = True
    def on_action_boss_key_toggled(self, checked: bool):
        self.ENABLE_BOSS_KEY = checked
        settings.cfg_common
    
    SEL_SHOW = False
    def on_action_hide_show_sel_wnd_triggered(self):
        for row in self.selected_rows:
            wk = settings.worker_list[row]
            if wk is None:
                continue
            Thread(target=self.thd_hide_show, args=(
                wk, self.SEL_SHOW), daemon=True).start()
        self.SEL_SHOW = not self.SEL_SHOW

    def thd_hide_show(self, wk: Worker, show: bool):
        self.do_hide_show(wk, show)

    def on_action_quit_triggered(self):
        ret = MyMsgBox("警告", "是否要退出本软件?", parent=self, reject=True).exec_()
        if ret == QMessageBox.AcceptRole:
            self.sig_close.emit()
        
    
    def on_edt_func_search_textChanged(self, text: str):
        if not text:
            iterator = QTreeWidgetItemIterator(self.tre_all)
            while iterator.value():
                item = iterator.value()
                item.setHidden(False)
                iterator += 1
        if not contains_chinese(text):
            return
        iterator = QTreeWidgetItemIterator(self.tre_all)
        while iterator.value():
            item = iterator.value()
            if text in item.text(0):
                self.tre_all.setCurrentItem(item)
                item.setHidden(False)
                if item.parent():
                    item.parent().setHidden(False)
            else:
                item.setHidden(True)
            iterator += 1
            
    def on_edt_func_search_returnPressed(self):
        task_name = self.tre_all.currentItem().text(0)
        self.lst_exec.addItem(task_name)
        self.edt_func_search.clear()
        
    def on_cmb_background_currentIndexChanged(self, index):
        settings.cfg_common["软件背景"] = index
        self.update()  # 重绘背景
        
    def on_slider_opaticy_valueChanged(self, value):
        settings.cfg_common["背景透明度"] = value
        self.update()  # 重绘背景
        
    def on_wnd_update_sig_update_finish(self):
        self.sig_close.emit()

    def on_timer_cur_timeout(self):
        dt_now = datetime.now(settings.china_tz)
        settings.cur_time_fmt = dt_now.strftime("%H:%M:%S")
        settings.cur_time_stamp = int(dt_now.timestamp())

    def on_timer_info_timeout(self):
        tip_list = [
            "如果你需要自动上号和掉线重连，才需要配置账号密码",
            "游戏窗口不要最小化，会导致读取图像失败，有需要可右键菜单隐藏",
            "表格菜单中的一键控五在不同列有不同效果哦，快去试试吧~",
            "执行速率调高会导致卡点几率上升, 只建议用在虎酒,百战,边关",
            "如果出现卡点, 一般是执行速率的问题, 把执行速率调低一点再试试",
            "如果你发现技能打不到自己身上, 请自行修正角色名",
            "请把重要物品上锁, 游戏若有画面卡顿不刷新等问题, 有可能会误丢物品",
            "每五个窗口为一队, 同颜色区域同时运行两个队长号会相互干扰的",
            "双击 日志列, 窗口行, 可以查看该窗口的历史日志",
            "如果游戏窗口显示不出来, 可重新双击 获取窗口列 表头(勾选排列窗口)",
        ]
        tip = tip_list[rnd(0, len(tip_list) - 1)]
        self.show_tip(tip)

    def on_timer_ds_timeout(self):        
        #  若距离上次心跳过去30分钟, 则退出软件
        if utils.delta_minute(self.last_heart_stamp, settings.cur_time_stamp) >= 30:
            self.show_info("与服务器断开连接...")
            self.sig_close.emit()
        # 判断用户是否危险
        settings.user_info["extra_info"] = get_user_action_info()
        if not self.chk_on_time.isChecked():
            return
        if self.chk_on_time_run_all.isChecked() and settings.cur_time_fmt[
            :5
        ] == self.tmedt_on_time_run_all.time().toString("HH:mm"):
            self.show_info("定时运行开始执行!")
            # 依次运行
            for wk in settings.worker_list:
                if wk:
                    self.tbe_console.item(wk.row, COL_RUN).setText(const.SELECTED)
        if self.chk_on_time_shut_down.isChecked() and settings.cur_time_fmt[:5]\
              == self.tmedt_on_time_shut_down.time().toString("HH:mm"):
            self.show_info("定时关机开始执行!")
            # 依次终止
            for wk in settings.worker_list:
                if wk:
                    self.tbe_console.item(wk.row, COL_END).setText(const.SELECTED)
            # 弹框提示
            msg_box = TimeMsgBox("提示", "定时关机时间到, 是否关机?", timeout=10, parent=self)
            msg_box.exec_()
            if msg_box.clickedButton() != msg_box.btn_accept:
                self.show_info("已取消关机")
                return
            # 确定关机
            settings.com_obj.exit_os()

    def on_tre_all_item_double_clicked(self, item, col):
        item_text = item.text(col)
        if item_text in ("单人功能", "团队功能", "免费功能", "增值功能"):
            return
        self.lst_exec.addItem(item_text)

    def on_lst_exec_item_double_clicked(self, item):
        row = self.lst_exec.row(item)
        self.lst_exec.takeItem(row)

    # 读取方案 (文件到控件)
    def on_lst_plan_item_clicked(self):
        self.show_msg = False
        cur_item = self.lst_plan.currentItem()
        if cur_item is None:
            self.show_info("失败,请选择要读取的配置文件!")
            return
        plan_name = cur_item.text()
        # 读取方案配置
        cfg_plan = copy.deepcopy(settings.default_cfg_plan)
        cur_cfg_plan = settings.cfg_plan_dict.get(plan_name, {})
        cfg_plan.update(cur_cfg_plan)
        # 设置到控件中
        self.read_basic_plan_setting_dict_to_control(cfg_plan)
        self.read_biz_task_setting_dict_to_control(plan_name)
        self.tab_set.setCurrentIndex(0)
        self.edt_func_search.clear()
        self.show_info(f"方案配置-{plan_name}, 读取完成")
        self.show_msg = True

    def on_lst_plan_currentTextChanged(self, cur_text):
        self.edt_plan_new_name.setText(cur_text)

    # 新建方案
    def on_btn_plan_create_clicked(self):
        plan_name = self.edt_plan_new_name.text()
        if plan_name == "":
            self.show_info("失败, 请先输入新方案名")
            return
        if plan_name in settings.cfg_plan_dict:  # 保存方案配置
            settings.cfg_plan_dict[plan_name] = self.get_basic_plan_setting_dict(
            )
            dict_to_json_file(settings.cfg_plan_dict, PATH_SOFTWARE_PLAN)
            return
        # 没有出现的方案，才创建
        settings.cfg_plan_dict[plan_name] = self.get_basic_plan_setting_dict()
        self.read_biz_task_setting_dict_to_control(plan_name)
        # 要保存到文件里
        dict_to_json_file(settings.cfg_plan_dict, PATH_SOFTWARE_PLAN)
        # 在lst_plan里添加方案名, 并排序
        self.lst_plan.addItem(plan_name)
        # 在每个cmb_plan里添加方案名的选项
        for row, cmb_plan in enumerate(settings.cmb_plan_list):
            cmb_plan.addItem(plan_name)
            if not cmb_plan.isEnabled():
                wk = settings.worker_list[row]
                if not wk:
                    cmb_plan.setCurrentIndex(-1)
        # 保存到json文件
        dict_to_json_file(settings.cfg_plan_dict, PATH_SOFTWARE_PLAN)
        self.cmb_set_plan_get_wnd.addItem(plan_name)
        self.cmb_set_plan_db_col.addItem(plan_name)
        self.show_info("新建方案成功!")

    # 重命名方案
    def on_btn_plan_rename_clicked(self):
        curItem = self.lst_plan.currentItem()
        if curItem is None:
            self.show_info("失败, 请先选择要重命名哪个方案!")
            return
        old_plan_name = curItem.text()
        new_plan_name = self.edt_plan_new_name.text()
        if new_plan_name == "":
            self.show_info("请先输入新方案名")
            return
        # 重命名key
        settings.cfg_plan_dict[new_plan_name] = settings.cfg_plan_dict.pop(
            old_plan_name
        )
        dict_to_json_file(settings.cfg_plan_dict, PATH_SOFTWARE_PLAN)
        # 在lst_plan里修改方案名
        self.lst_plan.takeItem(self.lst_plan.row(curItem))
        self.lst_plan.addItem(new_plan_name)
        # 在cmb_plan里修改方案名
        for cmb_plan in settings.cmb_plan_list:
            idx = cmb_plan.findText(old_plan_name)
            if idx != -1:
                cmb_plan.removeItem(idx)
                cmb_plan.addItem(new_plan_name)
        idx = self.cmb_set_plan_get_wnd.findText(old_plan_name)
        if idx != -1:
            self.cmb_set_plan_get_wnd.removeItem(idx)
            self.cmb_set_plan_get_wnd.addItem(new_plan_name)
        idx = self.cmb_set_plan_db_col.findText(old_plan_name)
        if idx != -1:
            self.cmb_set_plan_db_col.removeItem(idx)
            self.cmb_set_plan_db_col.addItem(new_plan_name)
        self.show_info("重命名方案成功!")

    # 删除方案
    def on_action_lst_plan_del_triggered(self):
        cur_item = self.lst_plan.currentItem()
        if cur_item is None:
            self.show_info("请先选中你要删除的方案")
            return
        plan_name = cur_item.text()
        # 从 方案配置字典中移除该方案
        settings.cfg_plan_dict.pop(plan_name)
        dict_to_json_file(settings.cfg_plan_dict, PATH_SOFTWARE_PLAN)
        # 删除 方案列表中的对应项
        row = self.lst_plan.row(cur_item)
        self.lst_plan.takeItem(row)
        # 删除 所有方案选择下拉框中的对应项
        for cmb_plan in settings.cmb_plan_list:
            cur_idx = cmb_plan.findText(plan_name)
            if cur_idx >= 0:  # 找到才删
                cmb_plan.removeItem(cur_idx)
        idx = self.cmb_set_plan_get_wnd.findText(plan_name)
        if idx != -1:
            self.cmb_set_plan_get_wnd.removeItem(idx)
        idx = self.cmb_set_plan_db_col.findText(plan_name)
        if idx != -1:
            self.cmb_set_plan_db_col.removeItem(idx)
        self.show_info("方案删除成功")
        
    def on_tbtn_custom_img_clicked(self):
        options = QFileDialog.Options()
        options |= QFileDialog.ReadOnly
        dialog = QFileDialog(self, "选择图片", "", "Images (*.png *.jpg *.bmp);All Files (*)", options=options)
        dialog.setFileMode(QFileDialog.ExistingFile)
        dialog.filesSelected.connect(self.update_back_img_path)
        dialog.open()
        
    def on_chk_deliver_yxb_stateChanged(self):
        if self.show_msg and self.chk_deliver_yxb.isChecked():
            MyMsgBox("提示", "勾选游戏币后, 其它物品会直接跳过, 每个号至少保留100W游戏币", parent=self).exec_()
        
        
    def on_btn_ji_he_clicked(self):
        MyMsgBox("吉禾送花说明", """\
窗口1的角色会送花给窗口2的角色, 窗口2会送给窗口3, 
以此类推...然后窗口5会送给窗口1, 每一个队都会像这样形成一个闭环. 
所以务必记得填写窗口对应的角色名, 如果背包里有花, 
就会送出其中的5朵, 如果少于5朵, 有几朵送几朵, 
如果背包没有花, 就会自动到寄售中购买最便宜的康乃馨买5个
                 """, parent=self).exec_()
        
    def on_btn_ding_shi_clicked(self):
        MyMsgBox("定时启动说明", """\
这是一个功能名, 要添加到执行列表才生效!
此功能只能限制它下面一个任务的启动时间, 
请注意它并不能让它上一个任务定时结束!
                 
执行列表中可以添加多个定时启动功能, 
每一个定时启动读的时间是不一样的, 
在配置区域中有三个时间可选, 会从上往下依次读取, 
第一个定时启动读的就是第一个时间, 
第二个定时启动读的是第二个时间, 以此类推, 
方便你配置方案完成一天的任务
(从上往下排第几个就用第几个时间, 
无论你是否勾选)
                 """, parent=self).exec_()
        
    def on_btn_bb_skill_clicked(self):
        MyMsgBox("随从打书说明", """\
先启动, 然后日志会提示你要打开BB页, 选好BB后 按下鼠标右键 就会开始, 
如果你已经打开BB页, 那么直接就会开始打书
书的话是有啥打啥, 建议开孔时背包不要放贵重书籍
                 """, parent=self).exec_()
        
    def on_btn_fu_ben_cou_shu_clicked(self):
        MyMsgBox("副本凑数说明", """\
把两个凑数的小号加好友分组到第四页, 第四页只能有这两个人, 
刷副本时小于3人会自动邀请凑数号, 凑数号自动同意进队, 
当进入副本后, 暂离, 副本结束时队长号发现其它人都暂离时, 
则离队, 凑数号发现 队伍解散了, 就自动离开副本,
下一个副本时队长又会重新创队再邀请这两个凑数的小号,
比如你有五个大号, 那就每个大号配两个小号, 共15个号
                 
用法: 
5个大号运行功能 人物副本 或 随从副本
10个小号运行功能 副本凑数
这样就相当于5个大号单刷,
你大号越多, 要配的小号就越多
                 """, parent=self).exec_()
        
    def on_btn_jin_zheng_clicked(self):
        MyMsgBox("金针穴道说明", """\
打火土穴道, 火剩最后一个, 剩余的全打土, 攻防兼备, 
请自备50个左右金针放背包里启动, 确保36个穴位全部打通,
不需要从火第一个穴道开始, 脚本会自动判断任意处都能继续
                 """, parent=self).exec_()
        
    def on_btn_back_city_team_clicked(self):
        MyMsgBox("团队回城说明", """\
从上到下按优先级排列，选遁甲优先用遁甲，
没有遁甲，且坐骑有归马技能优先用归马，
如果你选择下面的，有归马依旧优先用归马
无归马则从你的选项开始依次降级使用

如何充分发挥小遁甲的作用?
在小遁甲左侧NPC点位设置相对应的 脚本功能名, 
就会飞到任务NPC面前, 否则飞回开封回城点
                 """, parent=self).exec_()
        
    def on_btn_back_city_single_clicked(self):
        MyMsgBox("单人回城说明", """\
不需要你选择，会自动判断，
坐骑有归马技能优先用归马，
坐骑有游马技能优先用F8,
否则会依次尝试用门派票，开封票，
最后就是直接用F8，如果血内不足
会最终降级到跑步，会客栈补血内
会记忆，不会每次都从头试一遍
                 """, parent=self).exec_()
        
    def on_btn_lead_team_line_clicked(self):
        MyMsgBox("带队换线说明", """\
勾选后, 每周的富甲/大盗/擂台/边关等你就不用去设置换几线了,
比如执行到富甲会自动退队, 给同颜色块内的四个队员也发换线信号,
都换好一线后会重新组队, 达到带队换线的效果
如果你同颜色的窗口不是一队, 就不要勾选这个!!!
如是是一队, 队员方案的队员挂机功能配置一定要勾上`跟随队长换线`
                 """, parent=self).exec_()
        
    def on_btn_auto_skill_clicked(self):
        MyMsgBox("队伍协同说明", """\
如果你是单开混队, 请直接 强制使用, 因为你队长和队员
不是由脚本控制的, 所以读不到队长正在做的任务

五开的话请选自动判断模式
这样队员挂机bb是否用技能是根据队长任务来的, 
比如在队长在威望, 队员的人宠就都不会用技能, 
如果队长在名捕/闯关等, 队员的人宠就都会打技能，
当然, 前提是你队长和队员方案的"使用技能"都勾上

这里受队长任务影响的场景其实不止打技能, 
包括唤出BB, 领取双倍等都会受队长任务的影响
                 
注意:
队长和队员的窗口要在同一颜色范围内，
比如1-5行是一队，注意上号时要按顺序从上往下，
这样获取到的顺序也就是对的
                 """, parent=self).exec_()
        
    def on_btn_qi_hao_clicked(self):
        MyMsgBox("起号任务说明", """\
这个功能主要是拿来在0-10级新手任务的
后面扩展了一些, 让它能做所有的寻路对话类的任务,
比如门派任务, 主线任务等等,
只能做当前任务界面里选中的任务, 
如果中途遇到奇遇也会接, 会导致切任务
所以运行此功能时, 需要您时不时查看一下
若勾选了10级加门派, 则加完门派就自动终止了
                 """, parent=self).exec_()
        
    def on_btn_jiao_tao_zi_clicked(self):
        MyMsgBox("交桃子说明", """\
交桃子号: 用 交桃子功能, 勾上 切接货
补桃子号: 用 交货功能, 前面接 寻路对话 功能
        到桃花林 独孤猴集合交货, 交完桃子, 
        后面可以再接 单人刷野 继续刷桃子
这样 交桃子号会一直交, 直到背包交完切接货,
补桃子号会等待 交桃子号切到接货时才发起交易,
这样设置好, 整个过程就是全自动的
                 """, parent=self).exec_()
        
    def on_btn_bang_hui_clicked(self):
        MyMsgBox("帮会任务说明", """\
跑帮用法:
    任务号运行 帮会生产 功能, 
    材料号运行 帮会材料 功能
    下面是各个功能的介绍!

帮会生产: 找林冲接生产任务, 有些任务要找一些杂货
        和药材, 每次都自己去买或打太麻烦, 于是有
        了材料号(挂帮会材料功能), 所有的任务品会
        自动交易林冲旁边的材料号, 
        如果材料号旁边站了其它人会受影响, 
        可以在配置填上材料号名字避免被干扰
                 
帮会材料: 要自备帮会生产任务材料, 最好成组, 用小号
        刷, 到寄售买都可以, 全部备齐在背包, 然后
        启动帮会材料功能, 会跑到林冲旁边站着等待交易
        发货给那些跑生产的号
                 
帮会设施: 帮会可以升级后, 要找凌振交任务品
        提高升级的进度, 建议用帮会材料号再补点
        设施专门的任务品, 再运行 帮会设施
                 """, parent=self).exec_()
        
    def on_btn_kai_bao_he_clicked(self):
        MyMsgBox("开宝盒说明", """\
会打开背包里的所有 黑铁/白银/黄金/幽月 宝盒
还有 玄电/富甲天下/七夕/幸运 宝盒
还有 中秋的 经验/悟性 月饼
还有 圣诞的 圣诞袜
默认设置是用钥匙开,也可以自行修改用开山锤
                 """, parent=self).exec_()
        
    def on_btn_meng_jing_clicked(self):
        MyMsgBox("梦境寻宝说明", """\
会自动换二线找金鹰进入地图, 全自动跨图捡箱子转盘打怪,
目前是所有怪都会打, 不能指定打狂徒, 因为脚本是
靠识别地图黄点来打怪的, 黄点不打掉, 会影响找怪和跨图
                 """, parent=self).exec_()
        
    def on_btn_switch_primary_bb_clicked(self):
        MyMsgBox("切首发宠说明", """\
因为本软件是多任务连续执行, 为避免影响其它简单任务
的效率,切首发BB只在超级挑战/部分副本/闯关/极限等
难度较大的任务时才生效
                 """, parent=self).exec_()
        
    def on_btn_call_bb_clicked(self):
        MyMsgBox("唤出随从说明", """\
这个唤出随从是 极限/爬塔/大内/屠龙 共用的配置,
为避免控件大量重复, 所以抽出来, 只有这四个功能生效
请注意: 如果需要紫禁等超级挑战小于多少怪唤出BB的, 
请在团队配置-超级挑战中设置!!!
                 """, parent=self).exec_()
        
    def on_btn_chuang_guan_clicked(self):
        MyMsgBox("闯关说明", """\
注意本功能是半自动的, 需要你走到想打的关卡附近再启动
为什么不做成全自动?
首先, 你指定的关卡不一定是箱子关, 且箱子关不一定在你选的关的附近, 
    如果没打到箱子关, 奖励会少很多
其次, 由于在闯关场景中的游戏寻路是有bug的, 游戏官方
    的每个守将的位置都没录入对, 要做的话脚本就得自己录入
    非常麻烦, 再考虑到第一点, 故没有做, 请理解
    如果实在有需要 请联系作者定制(起步价1000元)
                 """, parent=self).exec_()
        
    def on_btn_people_use_skill_clicked(self):
        MyMsgBox("人物使用技能说明", """\
上面的刺吼是定时触发, 每隔指定回合必定触发,
如果不想定时触发, 请设为 99
然后把下面的继续刺吼勾上, 就是低于这个比例就
自动触发刺吼
这个比例是这样算的, 已刺吼怪数/总怪数, 
如果是只有极限教官一个怪的情况下, 这个比例只有
0%和100%两种情况, 所以你随便设置一个1~99%都可以实现
想要的效果, 等你爬塔时, 这个比例都不用改, 比如
10个怪至少刺中6个怪, 就设为60%
                 
五行克制要生效, 请在F1-F5依次放入 金 木 土!!! 水 火
按五行相克的顺序, 而不是按人们常说的顺序
少一个属性, 比如少林少金招, 则F1空着, 或者F1随便放个招
注意金招一定要放F1, 木招一定要放F2, 以此类推...
                 
护心放F6, 血内低于设定阈值会自动对自己使用
                 """, parent=self).exec_()
        
    def on_btn_listen_add_clicked(self):
        item = self.edt_listen.text()
        if not item:
            self.show_info("监听文本不能为空!")
            return
        self.lst_listen_world.addItem(item)

    def on_btn_listen_history_clicked(self):
        if not os.path.exists(FILE_LISTEN_LOG):
            file_create(FILE_LISTEN_LOG)
        full_path = os.path.join(os.getcwd(), FILE_LISTEN_LOG)
        os.startfile(full_path)

    def on_lst_listen_world_item_double_clicked(self, item):
        self.lst_listen_world.takeItem(self.lst_listen_world.row(item))
        
    def update_back_img_path(self, files):
        if files:
            file_name = files[0]
            self.back_img = file_name
            self.edt_back_img_path.setText(file_name)
            self.update()
        
    def on_chk_shuaye_terse_bb_stateChanged(self, state):
        if state != Qt.Checked:
            return
        # 获取当前的焦点控件
        focus_widget = QApplication.focusWidget()
        if focus_widget != self.chk_shuaye_terse_bb:
            return
        TimeMsgBox("注意", "精炼BB会把所有 白色麒麟 精炼 \n请务必把所有重要BB:\n改名 或 转金麒麟!!!\n", parent=self).exec_()

    def on_btn_cfg_save_clicked(self):
        idx = self.stack_widget.currentIndex()
        if idx in [0, 1]:
            self.common_cfg_save()
        elif idx == 2:
            self.plan_cfg_save()

    def on_btn_open_folder_game_path_clicked(self):
        dialog = QFileDialog()
        dialog.setFileMode(QFileDialog.AnyFile)
        if dialog.exec_():
            file_paths = dialog.selectedFiles()
            if not (file_paths and file_paths[0].endswith("update.exe")):
                self.show_info("请选择游戏根目录下的update.exe!")
                return
            exe_path = file_paths[0]
            self.edt_game_path.setText(exe_path)

    def on_btn_open_game_clicked(self):
        self.create_game_process()
        
    def create_game_process(self):
        update_path: str = self.edt_game_path.text()
        if not update_path:  # 如果没有输入路径
            self.show_info("请先输入游戏根目录下的update.exe!")
            return
        game_path_idx = update_path.find("update.exe")
        cwd = update_path[:game_path_idx-1]
        game_path = os.path.join(cwd, "bin/seasky.exe")
        server_name = self.cmb_game_server.currentText()
        print(f"server_name: {server_name}")
        da, va = constants.LINE_KONG_SERVER.get(server_name)
        cmd_line = CMD_LINE.format(da=da, va=va)
        pid = XProcess.create_process(cmd_line, cwd, game_path)
        print(f"pid: {pid}")
        if pid:
            for _ in range(10):
                time.sleep(0.2)
                hwnd = XProcess.get_hwnd_by_pid(pid)
                if hwnd:
                    print(f"hwnd: {hwnd}")
                    return hwnd
        return 0

    def on_exec_rate_slider_value_changed(self, value):
        settings.exec_rate = value / 100  # 在0.5 - 1.5之间
        self.lbe_exec_rate.setText("{:.1f}".format(settings.exec_rate))
        if value == 150:
            TimeMsgBox("脚本延迟已降低! CPU占用将升高!", "执行速率过快会导致部分任务异常, \n如有异常请切回1.0速率!!!", 
                       timeout=10,
                       parent=self).exec_()

    def on_h_header_double_clicked(self, col):
        log.info(f"第{col}列双击了")
        if col == COL_HWND:
            self.show_info("双击 [窗口句柄] 表头, 一键获取所有窗口")
            self.one_key_get_wnd()

        elif col == COL_PLAN:
            plan_idx = int(settings.cfg_common["双击方案列设置方案"])
            self.show_info(f"双击 [方案选择] 表头, 一键设置为{plan_idx}号方案")
            self.one_key_set_plan(plan_idx)

        elif col == COL_RUN:
            self.show_info("双击 [运行] 表头, 一键运行表格上的所有窗口")
            for wk in settings.worker_list:
                if wk is None:
                    continue
                self.tbe_console.item(wk.row, col).setText(SELECTED)

        elif col == COL_PAUSE:
            self.show_info("双击 [暂停] 表头, 一键暂停表格上的所有窗口")
            for wk in settings.worker_list:
                if wk is None:
                    continue
                if self.tbe_console.item(wk.row, COL_RUN).text():
                    self.tbe_console.item(wk.row, col).setText(SELECTED)

        elif col == COL_END:
            self.show_info("双击 [终止] 表头, 一键终止表格上的所有窗口")
            for wk in settings.worker_list:
                if wk is None:
                    continue
                if (
                    self.tbe_console.item(wk.row, COL_RUN).text()
                    or self.tbe_console.item(wk.row, COL_PAUSE).text()
                ):
                    self.tbe_console.item(wk.row, col).setText(SELECTED)

        elif col == COL_LOG:
            self.show_info("双击 [日志] 表头, 获取软件日志记录")
            # 读取内容, 设置内容
            content = file_read_content(os.path.join(PATH_SOFTWARE_LOG, 'info.log'))
            self.tbr_log.setText(content)
            # 设置标题, 将光标移到文档末, 显示
            self.diag.setWindowTitle(f"日志-本软件")
            self.tbr_log.moveCursor(QTextCursor.End)
            self.diag.show()
            self.diag.activateWindow()

    def on_v_header_double_clicked(self, row):
        log.info(f"第{row}行双击了")
        wk = settings.worker_list[row]
        if not wk:
            return
        self.show_info("双击 垂直 表头, 显示/隐藏该窗口")
        self.do_hide_show(wk)

    def on_tbe_console_cellClicked(self, row, col):
        row_num = row + 1
        if col == COL_HWND:  # 若双击"窗口"列
            self.show_info(f"单击 [窗口] 列,第{row_num}行, 显示/隐藏该窗口")
            wk = settings.worker_list[row]
            self.do_hide_show(wk)

    def on_tbe_console_cellDoubleClicked(self, row, col):
        row_num = row + 1
        if col == COL_HWND:  # 若双击"窗口"列
            self.show_info(f"双击 [窗口] 列,第{row_num}行, 显示该窗口")
            wk = settings.worker_list[row]
            if wk:
                wk.show_wnd()
                wk.set_window_state(wk.hwnd, "恢复")
                activate_wnd(wk.hwnd)

        elif col == COL_RUN:  # 若双击"运行"列
            self.show_info(f"双击 [运行] 列,第{row_num}行, 运行该窗口")
            self.tbe_console.currentItem().setText(SELECTED)

        elif col == COL_PAUSE:  # 若双击"暂停"列
            self.show_info(f"双击 [暂停] 列,第{row_num}行, 暂停该窗口")
            self.tbe_console.currentItem().setText(SELECTED)

        elif col == COL_END:  # 若双击"终止"列
            self.show_info(f"双击 [终止] 列,第{row_num}行, 终止该窗口")
            self.tbe_console.currentItem().setText(SELECTED)

        elif col == COL_LOG:  # 若双击"日志"列
            self.show_info(f"双击 [日志] 列,第{row_num}行, 显示该窗口的执行日志")
            # 先清除当前tbr日志的内容
            self.tbr_log.clear()
            # 读取内容, 设置内容
            content = file_read_content(
                f"{PATH_SOFTWARE_LOG}\\wnd_{row_num}.txt")
            self.tbr_log.setText(content)
            # 设置标题, 将光标移到文档末, 显示
            self.diag.setWindowTitle(f"日志-窗口{row_num}")
            self.tbr_log.moveCursor(QTextCursor.End)
            self.diag.show()
            self.diag.activateWindow()
        
        elif col in [COL_NAME, COL_ACCOUNT, COL_PASSWORD]:  # 账号
            self.tbe_console.editItem(self.tbe_console.currentItem())


    def do_hide_show(self, wk: Worker, show=False):
        if not wk:
            return
        try:
            x, y = get_wnd_pos(wk.hwnd)
        except:
            wk.record("窗口已不存在")
            self.rmv_wnd_from_console(wk)
            return
        if not show and x != HIDE_X:
            wk.hide_wnd(x, y)
        else:
            wk.show_wnd()
            activate_wnd(wk.hwnd)
                

    def on_tbe_console_itemChanged(self, item):
        col = item.column()
        if item.text() == "" or col not in [COL_RUN, COL_PAUSE, COL_END, COL_ACCOUNT]:
            return
        row = item.row()
        
        wk = settings.worker_list[row]
        if not wk and col != COL_ACCOUNT:
            self.tbe_console.item(row, col).setText("")
            return
                
        info = base64.b64decode("6LaF5Ye65Y2h5a+G5p2D55uKLCDlkK/liqjnqpflj6PlpLHotKU=")  # "超出卡密权益, 启动窗口失败"
        info = info.decode("utf-8")
        if col == COL_RUN:
            if self.count_run_num() > settings.card_rights:
                wk.record(info)
                self.show_info(info)
                self.tbe_console.item(row, col).setText("")
                return
            self.tbe_console.item(row, COL_PAUSE).setText("")
            self.tbe_console.item(row, COL_END).setText("")
            Thread(target=self.thread_run, args=(wk,), daemon=True).start()

        elif col == COL_PAUSE:
            if self.tbe_console.item(row, COL_RUN).text():  # 运行中才能暂停
                self.tbe_console.item(row, COL_RUN).setText("")
                self.tbe_console.item(row, COL_END).setText("")
                Thread(target=self.thread_pause, args=(wk,), daemon=True).start()
            else:
                self.tbe_console.item(row, COL_PAUSE).setText("")

        elif col == COL_END:
            if (
                self.tbe_console.item(row, COL_RUN).text()
                or self.tbe_console.item(row, COL_PAUSE).text()
            ):  # 运行或暂停中才能终止
                Thread(target=self.thread_end, args=(wk,), daemon=True).start()
            else:
                self.tbe_console.item(row, COL_END).setText("")

        elif col == COL_ACCOUNT:
            itemText = self.tbe_console.item(row, COL_ACCOUNT).text()
            if row == self.tbe_console.rowCount() - 1 and itemText:
                new_row = row+1
                self.tbe_console.insertRow(new_row)
                cmb_plan, cmb_server = self.tbe_console.add_row_items(new_row)
                settings.cmb_plan_list.append(cmb_plan)
                settings.cmb_server_list.append(cmb_server)
                # 添加内容
                for plan_name, _ in settings.cfg_plan_dict.items():
                    self.cmb_add_item(cmb_plan, plan_name, add_empty=True)
                for server in self.server_list:
                    self.cmb_add_item(cmb_server, server)
                # 添加信号槽
                cmb_plan.currentTextChanged.connect(self.on_cmb_plan_cur_text_changed)
                # 增加占位
                settings.worker_list.append(None)
                settings.hwnd_list.append(None)
                if new_row % 5 == 0:
                    settings.team_list.append(Team(new_row//5))


    def count_run_num(self):
        count = 0
        for wk in settings.worker_list:
            if wk is None:
                continue
            if self.tbe_console.item(wk.row, COL_RUN).text():
                count += 1
        return count

    # 方案改变
    def on_cmb_plan_cur_text_changed(self, plan_name):
        cmb_plan = self.sender()
        row = settings.cmb_plan_list.index(cmb_plan)
        wk: Worker = settings.worker_list[row]
        if wk is None:
            return
        self.update_worker_plan_info(wk, row, plan_name)

    def on_cmb_set_plan_db_col_cur_index_changed(self, idx):
        settings.cfg_common["双击方案列设置方案"] = idx
        self.show_info(f"双击方案列设置方案{idx}, 设置成功！")

    def on_cmb_arrange_get_wnd_cur_index_changed(self, idx):
        settings.cfg_common["获取窗口后排列方式"] = idx
        self.show_info(f"获取窗口后排列方式{idx}, 设置成功！")

    def on_cmb_set_plan_get_wnd_cur_index_changed(self, idx):
        settings.cfg_common["获取窗口后设置方案"] = idx
        self.show_info(f"获取窗口后设置方案{idx}, 设置成功！")

    def on_tab_set_currentChanged(self, idx):
        map_dict = {0: "基本设置", 1: "单人配置", 2: "团队配置", 3: "免费配置", 4: "增值配置",  5: "物价表"}
        self.show_tip(f"切换到 {map_dict[idx]} 页")

    def on_tool_bar_actionTriggered(self, action):
        if settings.is_first_switch:
            settings.is_first_switch = False
            self.send_request_heart_v2()
            self.one_key_get_wnd()
        action_name = action.text()
        self.show_tip(f"切换到 {action_name} 窗口")
        if action is self.action_readme:
            self.stack_widget.setCurrentIndex(0)
            self.action_console.setChecked(False)
            self.action_plan.setChecked(False)
        if action is self.action_console:
            self.stack_widget.setCurrentIndex(1)
            self.action_readme.setChecked(False)
            self.action_plan.setChecked(False)
        if action is self.action_plan:
            self.stack_widget.setCurrentIndex(2)
            self.action_readme.setChecked(False)
            self.action_console.setChecked(False)

    def on_tool_bar_orientationChanged(self, orientation):
        if orientation == Qt.Horizontal:
            self.tool_bar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        else:
            self.tool_bar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
            
    def on_chk_hide_game_task_bar_icon_stateChanged(self, state):
        self.do_hide_show_task_bar_icon(state)

    def do_hide_show_task_bar_icon(self, state):
        self_hwnd = self.winId()
        if state == Qt.Checked:
            for wk in settings.worker_list:
                if wk is None:
                    continue
                show_task_bar_icon(wk.hwnd, False)
            show_task_bar_icon(self_hwnd, False)
            self.chk_hide_game_task_bar_icon.setChecked(True)
        else:
            for wk in settings.worker_list:
                if wk is None:
                    continue
                show_task_bar_icon(wk.hwnd, True)
            show_task_bar_icon(self_hwnd, True)
            self.chk_hide_game_task_bar_icon.setChecked(False)

    def on_action_copy_exec_list_triggered(self):
        self.exec_list_copy = [
            self.lst_exec.item(idx).text() for idx in range(self.lst_exec.count())
        ]
        self.exec_list_copy_checked = [
            self.lst_exec.item(idx).isChecked() for idx in range(self.lst_exec.count())
        ]
        if self.exec_list_copy:
            self.show_info("执行列表复制成功")
            self.action_paste_exec_list.setEnabled(True)

    def on_action_paste_exec_list_triggered(self):
        self.lst_exec.clear()
        self.lst_exec.addItems(self.exec_list_copy)
        for row, checked in enumerate(self.exec_list_copy_checked):
            self.lst_exec.item(row).setChecked(checked)
        self.show_info("执行列表粘贴成功")
        
    def on_action_append_func_triggered(self):
        func_name =self.lst_exec.currentItem().text()
        if not func_name:
            self.show_info("请选择要追加的功能")
            return
        self.lst_exec.addItem(func_name)
        self.show_info(f"已追加功能 {func_name} 到执行列表")
        
    def on_action_del_func_triggered(self):
        item = self.lst_exec.currentItem()
        return self.on_lst_exec_item_double_clicked(item)
    
    def on_action_cancel_func_triggered(self):
        for idx in range(self.lst_exec.count()):
            self.lst_exec.item(idx).setChecked(False)
        
    def send_request_heart_v2(self):
        req_ts = str(settings.cur_time_stamp)
        self.thd_heart_beat = ThreadSendRequestHeartBeatV2(
            card_number=self.edt_card_key.text().rstrip(),
            machine_code=settings.machine_code,
            user_info=json.dumps(settings.user_info),
            req_ts=req_ts,
            client_version=CLIENT_VERSION,
            x_check_sum=get_check_sum(settings.machine_code, settings.server_host_name + req_ts),
        )
        self.thd_heart_beat.sig_resp_heart_done.connect(self.on_resp_heart_v2)
        self.thd_heart_beat.start()
    
    def on_resp_heart_v2(self, req_ts, status_code, resp, need_verify=True):
        print(f"on_resp_heart_v2 resp:\n{resp}")
        if status_code == 200:
            self.heart_fail_count = 0
            server_check_sum = resp.check_sum if resp else ""
            if need_verify and self.verify_server_check_sum(server_check_sum, req_ts):
                # 正常则刷新权益
                due_ts = int(resp.due_ts) if resp else 0
                # print(f"resp.card_rights:", resp.card_rights)
                resp_card_rights = {
                    0: "One",
                    1: "Five",
                    2: "Ten",
                    3: "Thirty",
                    4: "Twenty",
                    5: "Sixty",
                }.get(resp.card_rights, "One")
                settings.extra_rights = getattr(resp, 'card_extra_rights', 0)
                self.sig_rights.emit(resp_card_rights, due_ts == 0)
                if settings.is_first_heart:  # 首次的要展示一些信息
                    settings.is_first_heart = False
                    if due_ts == 0:
                        settings.due_ts = 0
                        self.show_info(f"欢迎使用{APP_NAME}-单开免费版")
                    elif due_ts == -1:
                        settings.due_ts = -1
                        self.show_info(f"欢迎使用{APP_NAME}-单开VIP版")
                    else:
                        settings.due_ts = due_ts
                        time_str = time_stamp_to_time_str(due_ts)
                        self.show_info(f"卡密到期时间:{time_str}")
                return
        elif status_code >= 500:  # 5XX
            if settings.is_first_heart:
                self.show_info("卡密使用失败，请稍后再试")
            else:
                log.warn("与服务器通信异常...")
            self.heart_fail_count += 1
            if self.heart_fail_count >= 5:
                self.sig_rights.emit(1, True)
                self.show_info("服务器繁忙!")
        elif status_code >= 400:
            self.show_info(f"卡密使用失败, 原因:{resp}")
            msg_box = TimeMsgBox("提示", f"卡密使用失败,原因:{resp}", timeout=3, parent=self)
            msg_box.exec_()
            self.sig_close.emit()
        
    def send_request_unbind(self):
        card_number = self.edt_card_key.text().rstrip()
        if not card_number:
            self.show_info("空卡密不能解绑")
            return
        if not validate_card_number(card_number):
            self.show_info("无效的卡密")
            return
        path = '/api/netauth/v1/card/unbind'
        url = settings.protocal + settings.server_host_name + path
        body = {
            "card_number": card_number,
            "machine_code": settings.machine_code,
        }
        self.thd_unbind = ThreadSendRequestUnBind(url, body)
        self.thd_unbind.sig_resp_unbind_done.connect(self.on_resp_unbind)
        self.thd_unbind.start()
        
    def on_resp_unbind(self, status_code, json_resp):
        if status_code == 200:
            msg_box = TimeMsgBox("提示", "解绑成功, 即将退出...", timeout=2, parent=self)
            msg_box.exec_()
            self.sig_close.emit()
        elif 400 <= status_code < 500:
            reason = json_resp.get("message", "未知错误")
            msg_box = TimeMsgBox("提示", f"解绑失败,原因:{reason}", parent=self)
            msg_box.exec_()
        else:
            self.show_info("解绑失败，服务器异常，稍后再试")
            
    def send_request_unbind_v2(self):
        card_number = self.edt_card_key.text().rstrip()
        if not validate_card_number(card_number):
            self.show_info("无效的卡密")
            return
        self.thd_unbind = ThreadSendRequestUnBindV2(
            card_number=card_number,
            machine_code=settings.machine_code,
        )
        self.thd_unbind.sig_resp_unbind_done.connect(self.on_resp_unbind_v2)
        self.thd_unbind.start()
        
    def on_resp_unbind_v2(self, status_code, resp):
        # print(f"on_resp_unbind_v2 resp:\n{resp}, status_code:", status_code)
        if status_code == 200:
            msg_box = TimeMsgBox("提示", "解绑成功, 即将退出...", timeout=2, parent=self)
            msg_box.exec_()
            self.sig_close.emit()
        elif status_code ==  400 :
            msg_box = TimeMsgBox("提示", f"解绑失败,原因:{resp}", parent=self)
            msg_box.exec_()
        else:
            self.show_info("解绑失败，服务器异常，稍后再试")
            
    def send_request_get_update_info(self):
        path = '/api/netauth/v1/get_update_info'
        url = settings.protocal + settings.server_host_name + path
        body = {
            'card_number': settings.card_number,
            'machine_code': settings.machine_code,
            'client_version': CLIENT_VERSION,
        }
        self.thd_check_update = ThreadCheckUpdate(url, body)
        self.thd_check_update.sig_get_download_info_finish.connect(lambda data: self.wnd_update.on_resp_update(data))
        self.thd_check_update.start()
        
    def send_request_get_update_info_v2(self):
        self.thd_check_update = ThreadCheckUpdateV2(
            card_number=settings.card_number, 
            machine_code=settings.machine_code, 
            client_version=CLIENT_VERSION,
        )
        self.thd_check_update.sig_get_download_info_finish.connect(lambda data: self.wnd_update.on_resp_update(data))
        self.thd_check_update.start()

    def send_request_get_custom_info_v2(self):
        self.thd_get_custom_info = ThreadGetCustomInfo(
            card_number=settings.card_number,
            machine_code=settings.machine_code,
            client_version=CLIENT_VERSION,
        )
        self.thd_get_custom_info.sig_get_custom_info_finish.connect(lambda data: self.on_resp_get_custom_info(data))
        self.thd_get_custom_info.start()

    def set_account_infos(self):
        # 新方法
        if not os.path.exists("config/account_infos.xdtop"):
            return
        with open("config/account_infos.xdtop", "rb") as f:
            encrypt_info = pickle.load(f)  # 使用 load() 读取
            json_str = decrypt(encrypt_info)
            account_info_dict = json.loads(json_str)
        # 定时器延时异步
        self.account_queue = [acc for acc in account_info_dict if acc]
        self.current_account_index = 0
        # 创建定时器
        self.account_timer = QTimer(self)
        self.account_timer.setInterval(10)  # 10ms间隔
        self.account_timer.timeout.connect(self.emit_next_account)
        self.account_timer.start()
        

    def emit_next_account(self):
        if self.current_account_index < len(self.account_queue):
            # 发送当前账户信号
            cfg_account = self.account_queue[self.current_account_index]
            self.sig_account.emit(cfg_account)
            self.current_account_index += 1
        else:
            # 所有账户已发送完毕，停止定时器
            self.account_timer.stop()
            # 可选：清理资源
            del self.account_queue
            del self.current_account_index
            
    def clear_card_key(self):
        self.edt_card_key.setText("")
        self.common_cfg_save()

    def verify_server_check_sum(self, server_check_sum, req_ts):
        if os.getenv("LOCAL_YT_DEBUG") == "1":
            return True
        # 服务端的校验和: key机器码 value=请求时间+域名
        key = settings.machine_code
        value = req_ts+settings.server_host_name
        print(f"key:{key}, value:{value}")
        client_check_sum = get_check_sum(key, value)
        print(f"server_check_sum:{server_check_sum}, client_check_sum:{client_check_sum}")
        return client_check_sum == server_check_sum
    
    def on_resp_get_custom_info(self, resp_dict: dict):
        constants.LINE_KONG_SERVER = resp_dict.get('game_servers') or constants.LINE_KONG_SERVER
        self.cmb_game_server.clear()
        for game_server in constants.LINE_KONG_SERVER.keys():
            self.cmb_game_server.addItem(game_server)
        server = settings.cfg_common.get("区服")
        if server and server in constants.LINE_KONG_SERVER:
            self.cmb_game_server.setCurrentText(server)

    def on_btn_card_use_clicked(self):
        settings.card_rights = 1
        settings.is_free = True
        settings.card_number = self.edt_card_key.text().rstrip()
        if not validate_card_number(settings.card_number):
            MyMsgBox("提示", "卡密无效", self).exec_()
            return
        self.send_request_heart_v2()
        self.common_cfg_save(show_log=False)
        MyMsgBox("提示", "卡密使用成功", self).exec_()

    def on_btn_card_unbind_clicked(self):
        self.send_request_unbind_v2()
        
    def on_btn_official_clicked(self):
        webbrowser.open(settings.official_url)

    def on_btn_check_update_clicked(self):
        self.wnd_update.show()
        self.send_request_get_update_info_v2()

    def on_action_five_game_control_triggered(self):
        start_row = self.start_row
        start_col = self.start_col
        if start_col == COL_HWND:  # 显示/隐藏
            self.show_info("窗口列控五，自动显示/隐藏此区域五窗口")
            action_show = False
            for row in range(start_row, start_row + 5):
                wk = settings.worker_list[row]
                if wk is None:
                    continue
                x, _ = get_wnd_pos(wk.hwnd)
                action_show = True if x == HIDE_X else False
                break
            for row in range(start_row, start_row + 5):
                wk = settings.worker_list[row]
                if wk is None:
                    continue
                Thread(target=self.thd_hide_show, args=(
                    wk, action_show), daemon=True).start()
        elif start_col == COL_PLAN:  # 设置方案
            self.show_info("方案列控五，一键设置方案")
            for row in range(start_row, start_row + 5):
                self.set_plan(row, int(settings.cfg_common["双击方案列设置方案"]))
        elif start_col == COL_RUN:  # 运行
            self.show_info("运行列控五，自动运行此区域五窗口")
            for row in range(start_row, start_row + 5):
                self.tbe_console.item(row, COL_RUN).setText(SELECTED)
        elif start_col == COL_PAUSE:  # 暂停
            self.show_info("暂停列控五，自动暂停此区域五窗口")
            for row in range(start_row, start_row + 5):
                self.tbe_console.item(row, COL_PAUSE).setText(SELECTED)
        elif start_col == COL_END:  # 终止
            self.show_info("终止列控五，自动终止此区域五窗口")
            for row in range(start_row, start_row + 5):
                self.tbe_console.item(row, COL_END).setText(SELECTED)
        elif start_col == COL_LOG:  # 强制退出
            self.show_info("日志列控五，自动强退此区域五窗口")
            ret = MyMsgBox("警告", "是否要强制结束此区域五窗口?",
                           parent=self, reject=True).exec_()
            if ret != QMessageBox.AcceptRole:
                return
            for row in range(start_row, start_row + 5):
                wk = settings.worker_list[row]
                if not wk:
                    continue
                self.rmv_wnd_from_console(wk)
                terminate_wnd(wk.hwnd)
        elif start_col == COL_SERVER:
            self.show_info("线路列控五，一键切换线路")
            cur_row = self.tbe_console.currentRow()
            cmb = settings.cmb_server_list[cur_row]
            xianlu = cmb.currentText()
            for row in range(start_row, start_row + 5):
                cmb_server = settings.cmb_server_list[row]
                cmb_server.setCurrentText(xianlu)


    def on_action_clear_sel_wnd_triggered(self):
        for row in self.selected_rows:
            wk = settings.worker_list[row]
            if not wk:
                continue
            self.rmv_wnd_from_console(wk)

    def thd_force_exit(self, wk: Worker):
        if not wk.is_bind(wk.hwnd):
            wk.bind_window()
        wk.key_press(VK_ESC)
        wk.find_pic_click(*RECT_FULL, "退出游戏.bmp", timeout=400)
        TaskBase.click_confirm(wk, timeout=300)
        self.rmv_wnd_from_console(wk)
        terminate_wnd(wk.hwnd)
    
    def on_action_force_exit_sel_wnd_triggered(self):
        ret = MyMsgBox("警告", "是否要强制结束选中游戏窗口?",
                           parent=self, reject=True).exec_()
        if ret != QMessageBox.AcceptRole:
            return
        for row in self.selected_rows:
            wk = settings.worker_list[row]
            if not wk:
                continue
            Thread(target=self.thd_force_exit, args=(wk,), daemon=True).start()
            
    def on_action_kill_all_wnd_triggered(self):
        ret = MyMsgBox("警告", "是否要强制结束所有游戏窗口?",
                           parent=self, reject=True).exec_()
        if ret != QMessageBox.AcceptRole:
            return
        for wk in settings.worker_list:
            if wk is None:
                continue
            Thread(target=self.thd_force_exit, args=(wk,), daemon=True).start()

    def on_action_login_sel_wnd_triggered(self):
        print(f"on_action_login_sel_wnd_triggered, selected_rows: {self.selected_rows}")
        start_row = self.selected_rows[0]
        end_row = self.selected_rows[-1]
        self.login_game_wnd_range(start_row, end_row+1)
        
    def login_game_wnd_range(self, start_row, end_row):
        game_path = self.edt_game_path.text()
        if not game_path:
            self.show_info("请先设置游戏路径")
            return
        if self.btn_start_stop_login.text() == "开始登录":
            if getattr(self, 'game_login_thread', None) and self.game_login_thread.isRunning():
                return
            self.btn_start_stop_login.setText("停止登录")
            self.game_login_thread = ThreadLogin(
                game_path, start_row, end_row)
            self.game_login_thread.start()

    def on_action_five_hide_show_triggered(self):
        start_row = self.start_row
        for row in range(start_row, start_row + 5):
            wk = settings.worker_list[row]
            if not wk:
                continue
            x, y = get_wnd_pos(wk.hwnd)
            if x != HIDE_X:  # 在显示则隐藏
                wk.hide_wnd(x, y)
            else:  # 在隐藏则显示
                wk.show_wnd()

    def update_worker_plan_info(self, wk: Worker, row: int, plan_name: str):
        old_plan_name = wk.plan_name
        wk.plan_name = plan_name
        wk.cfg_plan = copy.deepcopy(settings.default_cfg_plan)
        wk.cfg_plan.update(settings.cfg_plan_dict.get(plan_name, {}))
        if wk.cur_task:
            wk.cfg_plan_task = wk.cfg_plan[wk.cur_task]
        TaskBase.update_wk_fight_pb_info(wk)
        self.update_cmb_plan_tooltip(row)
        if old_plan_name != plan_name:
            wk.record(f"窗口已更换为方案:{plan_name}")
        else:
            wk.record(f"当前方案更新已生效")

    def thread_run(self, wk: Worker):
        if wk.is_pause:  # 若暂停, 则恢复
            if wk.thread:
                wk.thread.resume()
            wk.record("窗口已恢复运行")
        elif wk.is_end:  # 若结束, 才运行
            if wk.bind_window():
                wk.record("窗口开始运行")
                wk.thread = ThreadExec(wk)
                wk.thread.start()

    def thread_pause(self, wk: Worker):
        wk.record("窗口即将暂停...")
        if wk.thread:
            wk.thread.pause()
        wk.record("窗口已暂停运行")

    def thread_end(self, wk: Worker):
        wk.record("窗口即将终止...")
        if wk.thread:
            if wk.thread.end():
                wk.show_in_tbe_console(COL_RUN, "")
                wk.show_in_tbe_console(COL_PAUSE, "")
                cmb_plan = settings.cmb_plan_list[wk.row]
                cmb_plan.setEnabled(True)
                wk.cur_task = ""
                wk.unbind_window()
                wk.record("窗口已终止运行")
            else:  
                wk.record("窗口终止失败")
                wk.show_in_tbe_console(COL_END, "")
